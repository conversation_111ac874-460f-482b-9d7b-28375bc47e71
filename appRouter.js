
import express from 'express';
import bodyParser from 'body-parser';
import path from 'path';
import { fileURLToPath } from 'url';
import config from './src/config.js';
import router from './src/component/router.js';
import logger from './src/component/logger.js';
import db from './src/component/db.js'

var app = express();
app.use(bodyParser.json());

app.use('/', router);
const __filename = fileURLToPath(import.meta.url);

const __dirname = path.dirname(__filename);
app.use(express.static(path.join(__dirname, 'static')));

await db.initDatabase();

logger.info("bridge webserver started......");

app.listen(config.port, () => {
	return console.log(`Express server is listening at http://localhost:${config.port} 🚀`);
});
