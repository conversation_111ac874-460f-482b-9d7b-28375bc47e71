
import config from './src/config.js';
import logger from './src/component/logger.js';
import db from './src/component/db.js'
import { signEthereum, submitEthereum, monitorEventEthereum } from "./src/ethereum/index.js";
import { monitorEventSui, signSui, submitSui } from './src/sui/index.js';

await db.initDatabase();

logger.info("relayer started......");

const NODE_ETH_SUI_SIGNER = 18;
const NODE_ETH_SUI_SENDER = 19;
const NODE_MONITOR_ALL = 90;

const funMap = {
    // eth - sui
    [NODE_ETH_SUI_SIGNER]: function () {
        logger.info("eth sui Signer Started");
        let transferSignature = false;
        if (config.scanEvent !== 'false') {
            monitorEventSui();
            monitorEventEthereum();
            transferSignature = true;
        }
        signEthereum(undefined, undefined, transferSignature);
        signSui(undefined, undefined, transferSignature);
    },
    [NODE_ETH_SUI_SENDER]: function () {
        logger.info("eth sui Sender Started");
        submitEthereum();
        submitSui();

        if (config.scanEvent !== 'false') {
            monitorEventSui();
            monitorEventEthereum();
        }
    },
    [NODE_MONITOR_ALL]: function () {
        if (config.ethereum.bridgeAddress) {
            monitorEventEthereum();
        }
        if (config.sui.packageId) {
            monitorEventSui();
        }
    },
}

function start() {
    const currentNodeType = parseInt(config.nodetype);
    if (!(currentNodeType in funMap)) {
        throw new Error("nodetype error, not support Node type");
    }

    const func = funMap[currentNodeType];
    func();
}

start();
