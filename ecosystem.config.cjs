/* eslint-disable no-undef */
module.exports = {
  apps: [
    {
      name: 'bitlayer-sui-transfer-sb1',
      script: 'app.js',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      env: {
        NODE_ENV: 'production',
        NODE_TYPE: 19,
        THRESHOLD: 2,
	      MYSQL_DBNAME: 'relayer_submitter',
        ETH_PK: '',
        SUI_PK: '',
      },
    },
    {
      name: 'bitlayer-sui-transfer-router',
      script: 'appRouter.js',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      env: {
        NODE_ENV: 'production',
        NODE_TYPE: 19,
        THRESHOLD: 2,
        MYSQL_DBNAME: 'relayer_submitter',
        ETH_PK: '',
        SUI_PK: '',
      },
    },
    {
      name: 'bitlayer-sui-transfer-sg1',
      script: 'app.js',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      env: {
        NODE_ENV: 'production',
        NODE_TYPE: 18,
        THRESHOLD: 2,
	      MYSQL_DBNAME: 'relayer_signer1',
        ETH_PK: '',
        SUI_PK: '',
      },
    },
    {
      name: 'bitlayer-sui-transfer-sg2',
      script: 'app.js',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      env: {
        NODE_ENV: 'production',
        NODE_TYPE: 18,
        THRESHOLD: 2,
	      MYSQL_DBNAME: 'relayer_signer2',
        ETH_PK: '',
        SUI_PK: '',
      },
    },
    {
      name: 'bitlayer-sui-transfer-sg3',
      script: 'app.js',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      env: {
        NODE_ENV: 'production',
        NODE_TYPE: 18,
        THRESHOLD: 2,
	      MYSQL_DBNAME: 'relayer_signer3',
        ETH_PK: '',
        SUI_PK: '',
      },
    },
  ],
};