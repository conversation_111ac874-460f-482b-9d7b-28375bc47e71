{"name": "echo", "version": "0.0.0", "private": true, "main": "src/b2_event_monitor.js", "scripts": {"start": "node ./app.js", "generateSign": "node src/script/generateSign.js", "lint": "eslint ."}, "dependencies": {"@aptos-labs/ts-sdk": "^1.3.1", "@coral-xyz/anchor": "^0.29.0", "@iota/bcs": "^1.0.0", "@iota/iota-sdk": "^1.2.0", "@mysten/sui": "^1.22.0", "@solana/spl-token": "^0.4.8", "@solana/web3.js": "^1.98.0", "axios": "^1.4.0", "bn.js": "^5.2.1", "body-parser": "^1.20.1", "borsh": "^0.7.0", "bs58": "^6.0.0", "debug": "^4.3.4", "dotenv": "^16.3.1", "ethers": "^6.13.0", "express": "^4.18.2", "express-async-errors": "^3.1.1", "format": "^0.2.2", "js-sha3": "^0.9.3", "moment": "^2.29.4", "morgan": "^1.10.0", "mysql": "^2.18.1", "node-cache": "^5.1.2", "node-cron": "^3.0.2", "node-telegram-bot-api": "^0.61.0", "nodemailer": "^6.9.4", "patch-package": "^6.4.7", "redis": "^4.7.0", "run-parallel": "^1.2.0", "silly-datetime": "^0.1.2", "socks": "^2.7.1", "tweetnacl": "^1.0.3", "uuid": "^9.0.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "type": "module", "devDependencies": {"@eslint/js": "^9.18.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "globals": "^15.14.0", "prettier": "^3.4.2"}}