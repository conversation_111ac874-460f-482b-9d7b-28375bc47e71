import { config } from "dotenv";
import { getBytes } from "ethers";
import { ethers } from "ethers";
import crypto from 'crypto';
import db from "./db.js";
import local_config from "../config.js";
import {
  Aptos,
  AptosConfig,
  sleep
} from "@aptos-labs/ts-sdk";
import { createClient } from 'redis';
import sha from "js-sha3";
import Base58 from 'bs58';
const { sha3_256 } = sha
import { sendEventMsg } from "../network/network_informer.js"
import { SIGN_MESSAGE_TYPE, SIGN_VERSION, CHAINID_MORPHL, CHAINID_SOLANA, STATE_FAILED } from "../const.js";

const MESSAGE_PREFIX = "APTOS_BRIDGE_MESSAGE"

const faucetSetKey = "echo_relayer:aptos:faucet_set";

const redisClient = await createClient({
  url: process.env.RedisUrl
}).on('error', err => console.log('Redis Client Error', err))
  .connect();

export default {
  set_limit_amount_and_count: set_limit_amount_and_count,
  sha3_256_btc_data: sha3_256_btc_data,
  timestamp_convert: timestamp_convert,
  delay: function (time) {
    return new Promise((resolve) => setTimeout(resolve, time));
  },
};

export function message_encode_sol(message, payload) {
  const encodedMessage = ethers.solidityPacked(
    ["string", "uint8", "uint8", "uint64", "uint8", "bytes"],
    [MESSAGE_PREFIX, message.messageType, message.version, message.nonce, message.chainID, message.payload]
  );

  return encodedMessage
}

export function message_payload_encode_sol(senderAddress, targetChain, recipientAddress, tokenID, amount) {
  let sa = senderAddress;

  if (!sa.startsWith('0x')) {
    throw new Error("Invalid sender address");
  }

  while (sa.length < 66) {
    sa = sa.replace("0x", "0x0");
  }

  const payload = ethers.solidityPacked(
    ["uint8", "bytes", "uint8", "uint8", "address", "uint8", "uint64"],
    [32, sa, targetChain, 20, recipientAddress, tokenID, amount]
  );

  return payload
}


export async function message_sign_sol(message, pk) {
  const messageHash = ethers.keccak256(message);
  const wallet = new ethers.Wallet(pk);
  const signature = await wallet.signMessage(getBytes(messageHash));
  console.log(signature)
  return signature;
}

export async function message_verify_sign(signatures, message) {
  const messageHash = ethers.keccak256(message);
  const signerAddress = ethers.verifyMessage(ethers.getBytes(messageHash), signatures)
  console.log(signerAddress)
  return signerAddress;
}

// export async function message_sign_sol(message, pk) {
//   const messageHash = ethers.solidityPackedKeccak256(['string'], [message])
//   const wallet = new ethers.Wallet(pk);
//   const signature = await wallet.signMessage(ethers.getBytes(messageHash));
//   return signature;
// }

// export async function message_verify_sign(signatures, message) {
//   const messageHash = ethers.hashMessage(ethers.getBytes(ethers.solidityPackedKeccak256(['string'], [message])))
//   const signerAddress = ethers.recoverAddress(ethers.getBytes(messageHash), signatures)
//   return signerAddress;
// }

export function message_sign_apt(pk, msg) {

  const signingKey = new ethers.SigningKey(pk);

  const wallet = new ethers.Wallet(pk);
  console.log("adddress: " + wallet.address)

  console.log(`pubkey: ${signingKey.publicKey}`);
  console.log(`pubkey compressed: ${signingKey.compressedPublicKey}`);

  const msgHash = ethers.keccak256(ethers.getBytes(msg));
  const signature = signingKey.sign(msgHash);

  console.log(`hash: ${msgHash}`);
  console.log(`sign: ${signature.serialized}`);

  return signature.serialized;
}

export async function message_verify_apt(signatures, message) {
  const messageHash = ethers.keccak256(message);
  const signerAddress = ethers.recoverAddress(ethers.getBytes(messageHash), signatures)
  console.log(signerAddress)
  return signerAddress;
}

export async function getBalance() {

  var customWsProvider = new ethers.JsonRpcProvider(config.b2.url);
  let walletWithProvider = new ethers.Wallet(config.b2.recoveryPhrase, customWsProvider);
  const bridgeContract = new ethers.Contract(
    config.b2.valutAddress,
    JSON.parse(fs.readFileSync("./src/config/abi_valut.json", "utf8")).abi,
    walletWithProvider);

  const balance = await bridgeContract.getERC20Balance(config.b2.tokenAddress);
  console.log('Balance:', balance.toString());
  return parseInt(balance.toString());
}


export function generateSeqNumBigInt(sequence_number, txid) {
  // 将 txid 前十位转换为十进制
  const txidDecimal = BigInt("0x" + txid.slice(0, 10));
  // 将 sequence_number 转为字符串并附加 txid 的十进制表示
  const seqNum = BigInt(`${sequence_number}${txidDecimal}`);
  return seqNum;
}

export async function is_committee_b2(sig, ev) {

  let threshold = 0;
  let committee = JSON.parse(local_config.committee_b2)

  let seq = ev.sequence_number.toString()
  // if (ev.source_chain == 12) { seq }
  let fromAddress = ev.from_address;
  if (ev.source_chain == CHAINID_SOLANA) {
    // eslint-disable-next-line no-undef
    fromAddress = '0x' + Buffer.from(Base58.decode(fromAddress)).toString('hex');
  }
  const amount = BigInt(ev.amount.toString());
  const payload = message_payload_encode_sol(
    fromAddress,
    ev.target_chain,
    ev.target_address,
    ev.token_type,
    amount)

  const Message = {
    messageType: 0,
    version: 1,
    nonce: BigInt(seq.toString()),
    chainID: ev.source_chain,
    payload: payload
  };

  for (let i = 0; i < sig.length; i++) {

    const encodedMessage = message_encode_sol(Message, payload);
    // console.log(sig[i])
    const address = await message_verify_sign(sig[i], encodedMessage);

    // console.log("msgencode=", encodedMessage)
    // console.log("address=", address)

    for (let j = 0; j < committee.length; j++) {
      if (address.toLowerCase() == committee[j].toLowerCase()) { threshold = threshold + 1 }
    }
  }

  // console.log(threshold)
  if (threshold > local_config.threshold) { return true }
  else { return false }
}

export async function isCommitteeB2Single(singleSignature, ev) {
  const committee = JSON.parse(local_config.committee_b2);
  let seq = ev.sequence_number.toString();

  const amount = BigInt(ev.amount.toString());
  const payload = message_payload_encode_sol(
    ev.from_address,
    ev.target_chain,
    ev.target_address,
    ev.token_type,
    amount,
  );

  const Message = {
    messageType: 0,
    version: 1,
    nonce: BigInt(seq.toString()),
    chainID: ev.source_chain,
    payload: payload
  };

  const encodedMessage = message_encode_sol(Message, payload);
  const address = await message_verify_sign(singleSignature, encodedMessage);

  for (let j = 0; j < committee.length; j++) {
    if (address.toLowerCase() == committee[j].toLowerCase()) { 
      return true;
    }
  }
  return false;
}

export async function is_committee_morphl(sig, ev) {

  let threshold = 0;
  let committee = JSON.parse(local_config.committee_morphl)

  let seq = ev.sequence_number.toString()

  const amount = BigInt(ev.amount.toString());
  const payload = message_payload_encode_sol(
    ev.from_address,
    ev.target_chain,
    ev.target_address,
    ev.token_type,
    amount)

  const Message = {
    messageType: SIGN_MESSAGE_TYPE,
    version: SIGN_VERSION,
    nonce: BigInt(seq.toString()),
    chainID: ev.source_chain,
    payload: payload
  };

  for (let i = 0; i < sig.length; i++) {

    const encodedMessage = message_encode_sol(Message, payload);
    // console.log(sig[i])
    const address = await message_verify_sign(sig[i], encodedMessage);

    // console.log("msgencode=", encodedMessage)
    // console.log("address=", address)

    for (let j = 0; j < committee.length; j++) {
      if (address.toLowerCase() == committee[j].toLowerCase()) { threshold = threshold + 1 }
    }
  }

  return threshold > local_config.threshold;
}

export async function is_committee_sui(sig, ev) {
  let threshold = 0;
  let committee = local_config.sui.committees;
  let seq = ev.sequence_number.toString();
  const amount = BigInt(ev.amount.toString());
  const payload = message_payload_encode_sol(
    ev.from_address,
    ev.target_chain,
    ev.target_address,
    ev.token_type,
    amount,
  );

  const Message = {
    messageType: SIGN_MESSAGE_TYPE,
    version: SIGN_VERSION,
    nonce: BigInt(seq.toString()),
    chainID: ev.source_chain,
    payload: payload
  };

  for (let i = 0; i < sig.length; i++) {
    const encodedMessage = message_encode_sol(Message, payload);
    const address = await message_verify_sign(sig[i], encodedMessage);
    for (let j = 0; j < committee.length; j++) {
      if (address.toLowerCase() == committee[j].toLowerCase()) { 
        threshold = threshold + 1;
      }
    }
  }

  return threshold > local_config.threshold;
}

export async function is_committee_target_evm(sig, ev) {

  let threshold = 0;
  let committee = JSON.parse(local_config.target_ethereum.committee);
  let seq = ev.sequence_number.toString();

  const amount = BigInt(ev.amount.toString());
  const payload = message_payload_encode_sol(
    ev.from_address,
    ev.target_chain,
    ev.target_address,
    ev.token_type,
    amount,
  );

  const Message = {
    messageType: SIGN_MESSAGE_TYPE,
    version: SIGN_VERSION,
    nonce: BigInt(seq.toString()),
    chainID: ev.source_chain,
    payload: payload
  };
  for (let i = 0; i < sig.length; i++) {

    const encodedMessage = message_encode_sol(Message, payload);
    const address = await message_verify_sign(sig[i], encodedMessage);

    for (let j = 0; j < committee.length; j++) {
      if (address.toLowerCase() == committee[j].toLowerCase()) { threshold = threshold + 1 }
    }
  }
  return threshold > local_config.threshold;
}

export async function is_committee_solana(sigs) {
  let threshold = 0;
  const committees = local_config.solana.committees;
  const committeeMap = {};
  for (const committee of committees) {
    committeeMap[committee] = true;
  }
  for (const sig of sigs) {
    if (committeeMap[sig.publicKey]) {
      threshold++;
    }
  }
  return threshold > local_config.threshold;
}

export async function is_committee_apt(sig, ev) {

  const aptosConfig = new AptosConfig({ network: local_config.aptos.network, clientConfig: {API_KEY: local_config.aptos.api_key}  });
  const aptos = new Aptos(aptosConfig);

  let committee = JSON.parse(local_config.committee_apt)

  const payload = {
    function: `${local_config.aptos.bridgeAddress}::message::concat_token_msg`,
    functionArguments: [
      ev.source_chain,
      ev.sequence_number,
      ev.from_address,
      ev.target_chain,
      ev.target_address,
      ev.token_type,
      ev.amount,
    ]
  };

  let threshold = 0;

  for (let i = 0; i < sig.length; i++) {

    const chainId = await aptos.view({ payload });
    // console.log("msgencode=", chainId[0])
    // console.log("sig=", sig[i])

    const address = await message_verify_apt(sig[i], chainId[0]);

    for (let j = 0; j < committee.length; j++) {
      if (address.toLowerCase() == committee[j].toLowerCase()) { threshold = threshold + 1 }
    }
  }

  if (threshold > local_config.threshold) { return true }
  else { return false }
}

export async function isCommitteeAptSingle(singleSignature, ev) {
  const aptosConfig = new AptosConfig({ network: local_config.aptos.network, clientConfig: {API_KEY: local_config.aptos.api_key}  });
  const aptos = new Aptos(aptosConfig);
  const committee = JSON.parse(local_config.committee_apt);

  const payload = {
    function: `${local_config.aptos.bridgeAddress}::message::concat_token_msg`,
    functionArguments: [
      ev.source_chain,
      ev.sequence_number,
      ev.from_address,
      ev.target_chain,
      ev.target_address,
      ev.token_type,
      ev.amount,
    ]
  };
  const chainId = await aptos.view({ payload });
  const address = await message_verify_apt(singleSignature, chainId[0]);
  for (let j = 0; j < committee.length; j++) {
      if (address.toLowerCase() == committee[j].toLowerCase()) { 
        return true;
      }
  }
  return false;
}

export async function is_committee_move(sig, ev) {

  // const aptosConfig = new AptosConfig({ network: local_config.aptos.network });
  // const aptos = new Aptos(aptosConfig);
  const movementConfig = new AptosConfig(local_config.movement.network);
  const movement = new Aptos(movementConfig);

  let committee = JSON.parse(local_config.committee_movement)

  const payload = {
    function: `${local_config.movement.bridgeAddress}::message::concat_token_msg`,
    functionArguments: [
      ev.source_chain,
      ev.sequence_number,
      ev.from_address,
      ev.target_chain,
      ev.target_address,
      ev.token_type,
      ev.amount,
    ]
  };

  let threshold = 0;

  for (let i = 0; i < sig.length; i++) {

    const chainId = await movement.view({ payload });
    // console.log("msgencode=", chainId[0])
    // console.log("sig=", sig[i])

    const address = await message_verify_apt(sig[i], chainId[0]);

    for (let j = 0; j < committee.length; j++) {
      if (address.toLowerCase() == committee[j].toLowerCase()) { threshold = threshold + 1 }
    }
  }

  if (threshold > local_config.threshold) { return true }
  else { return false }
}

export async function checkIfSignAll(ev) {
  let redis_key;
  // in
  if (ev.source_chain == local_config.ethereum.chainId) {
    redis_key = `eth:limit:${ev.source_chain}`;
  }else if (ev.source_chain == local_config.sui.chainId) {
    redis_key = `sui:limit:${ev.source_chain}`;
  }else {
    await sendEventMsg("Unsupported source chain ", { chain: ev.source_chain }, 3);
    await db.updateBTCDepositEventWithFail(STATE_FAILED, ev.hash, 'Unsupported source chain ');
    await sleep(1000);
    return false;
  }

  const hash_set_key = redis_key + ev.source_chain + ":" + ev.from_address + ":set"
  // if added this hash return
  if (await redisClient.exists(hash_set_key) == 1 && await redisClient.sIsMember(hash_set_key, ev.hash) == 1) {
    return false
  }

  return true
}

export async function set_limit_amount_and_count(key, limit_amount, limit_count,
  source_address, source_chain_id, amount,
  to_address, to_chain, token_type, hash
) {

  const hash_set_key = key + ":set"

  // if added this hash return
  if (await redisClient.exists(hash_set_key) == 1 && await redisClient.exists(key) == 1
    && await redisClient.sIsMember(hash_set_key, hash) == 1) {
    return
  }

  const data = await redisClient.get(key)

  const warn_content = {
    source_address: source_address, source_chain_id: source_chain_id, amount: amount,
    to_address: to_address, to_chain: to_chain, token_type: token_type, hash: hash
  }

  // send warn
  if (BigInt(data.amount) > limit_amount) {

    warn_content.now_amount = data.amount
    warn_content.limit_amount = (limit_amount).toString()
    //todo
    await sendEventMsg("Someone crossed the bridge today and the amount reached the limit ", warn_content, 2)
    return
  }
  // send warn
  if (data.count > limit_count + 1) {

    warn_content.now_count = data.count
    warn_content.limit_count = limit_count
    await sendEventMsg("Someone has reached the limit for crossing the bridge today", warn_content, 2)
    return
  }

  // set redis data and expire
  await set_limit_redis_data(key, hash_set_key, amount, hash)
}

export async function set_b2_in_limit(key,
  source_address, source_chain_id, amount,
  to_address, to_chain, token_type, hash) {

  const b2_total_limit_amount = BigInt(process.env.B2_TOTAL_IN_LIMIT_AMOUNT);
  const b2_total_limit_count = parseInt(process.env.B2_TOTAL_IN_LIMIT_COUNT);

  const hash_set_key = key + ":set"

  // if added this hash return
  if (await redisClient.exists(hash_set_key) == 1 && await redisClient.exists(key) == 1
    && await redisClient.sIsMember(hash_set_key, hash) == 1) {
    return
  }

  // set redis data and expire
  const data = await set_limit_redis_data(key, hash_set_key, amount, hash)

  const warn_content = {
    b2_in_limit_total_count: (b2_total_limit_amount).toString(),
    b2_in_limit_total_amount: (b2_total_limit_count).toString(),
    user: {
      source_address: source_address, source_chain_id: source_chain_id, amount: amount,
      to_address: to_address, to_chain: to_chain, token_type: token_type, hash: hash
    }

  }

  // send warn
  if (BigInt(data.amount) > b2_total_limit_amount) {

    warn_content.user_now_amount = data.amount
    //todo
    await sendEventMsg("The total amount for crossing back to B2 is limited ", warn_content, 2)
  }
  // send warn
  if (data.count > b2_total_limit_count) {

    warn_content.user_now_count = data.count
    await sendEventMsg("The total number of times to cross back to B2 is limited ", warn_content, 2)
  }
}

async function set_limit_redis_data(key, hash_set_key, amount, hash) {

  let data = { "amount": BigInt(amount), count: 1 }

  let now_amount = 0
  const response = await redisClient.exists(key);
  if (response > 0) {
    data = await redisClient.get(key)
    data = JSON.parse(data)
    data.count = data.count + 1
    now_amount = BigInt(data.amount) + BigInt(amount)
    data.amount = (now_amount).toString()
    await redisClient.set(key, JSON.stringify(data));
    await redisClient.sAdd(hash_set_key, hash)

  } else {

    now_amount = data.amount
    data.amount = (data.amount).toString()
    await redisClient.set(key, JSON.stringify(data));
    await redisClient.sAdd(hash_set_key, hash)

  }

  const today = new Date();
  const time_now = parseInt(today.getTime() / 1000);
  // 设置小时、分钟和秒数为 23:59:59
  today.setHours(23, 59, 59, 999);
  // 输出结果
  const time_limit_today = parseInt(today.getTime() / 1000);

  const expir = time_limit_today - time_now
  // set expir
  await redisClient.expire(key, expir)
  await redisClient.expire(hash_set_key, expir)

  return data
}

export async function sha3_256_btc_data(ev) {

  // id + hash + sequenceNumber + sourceChain + fromAddress + sinAddress + targetChain + targetAddress + tokenType + originalAmount + amount + fee + version + timestamp + state
  const hash_data = {
    id: ev.id,
    hash: ev.hash,
    sequenceNumber: ev.sequence_number,
    sourceChain: ev.source_chain,
    fromAddress: ev.from_address,
    targetChain: ev.target_chain,
    targetAddress: ev.target_address,
    tokenType: ev.token_type,
    originalAmount: ev.original_amount,
    amount: ev.amount,
    fee: ev.fee,
    version: ev.version,
    timestamp: ev.timestamp,
    state: ev.state
  }

  const sortedKeys = Object.keys(hash_data).sort().reverse();
  const result = sortedKeys.map(key => `${key}${hash_data[key]}`).join('');
  const finalString = `ECHOVERIFY${result}`;
  console.log(finalString)
  return sha3_256(finalString);
}

export function timestamp_convert(timestamp) {

  timestamp = "" + timestamp

  if (timestamp.length == 13) {
    return parseInt(parseInt(timestamp) / 1000)
  }

  if (timestamp.length == 16) {
    return parseInt(parseInt(timestamp) / 1000000)
  }

  return parseInt(timestamp)
}

export function gethash(string) {
  const utf8 = new TextEncoder().encode(string);
  return crypto.subtle.digest('SHA-256', utf8).then((hashBuffer) => {
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray
      .map((bytes) => bytes.toString(16).padStart(2, '0'))
      .join('');
    return '0x' + hashHex;
  });
}

export function getChainAndTokenKey(tokenType) {
  // 对应环境变量里面的 CHECK_LIMIT_CHAIN_TOKENS 格式
  return `${tokenType}`; 
}

export async function getHashPrefix(value) {
  const encoder = new TextEncoder();
  const data = encoder.encode(value);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map(byte => byte.toString(16).padStart(2, '0')).join('');
  return hashHex.substring(0, 8);
} 

export async function setSolanaScanTransactionSignature(signature) {
  const key = `solana:scan:transaction:latest_signature:${local_config.solana.bridgeAddress}`;
  await redisClient.set(key, signature);
}

export async function getSolanaScanTransactionSignature() {
  const key = `solana:scan:transaction:latest_signature:${local_config.solana.bridgeAddress}`;
  const data = await redisClient.get(key);
  return data;
}

export async function setSolanaLUT(lut) {
  const key = `solana:sender:lut`;
  await redisClient.set(key, lut);
}

export async function getSolanaLUT() {
  const key = `solana:sender:lut`;
  const data = await redisClient.get(key);
  return data;
}

export async function setSuiCursor(cursor) {
  const key = `relayer:sui:cursor`;
  await redisClient.set(key, cursor);
}

export async function getSuiCursor() {
  const key = `relayer:sui:cursor`;
  const data = await redisClient.get(key);
  return data;
}

export async function setIotaCursor(cursor) {
  const key = `relayer:iota:cursor`;
  await redisClient.set(key, cursor);
}

export async function getIotaCursor() {
  const key = `relayer:iota:cursor`;
  const data = await redisClient.get(key);
  return data;
}

// setSuiCursor('');

export async function setAptosFaucetAddress(address) {
  await redisClient.sAdd(faucetSetKey, address);
}

export async function getAptosFaucetAddress(address) {
  const exist = await redisClient.sIsMember(faucetSetKey, address);
  return !!exist;
}

export function getRealAptosAddress(address) {
  return '0x' + address?.replace(/^0x0*/, '');
}

export async function getHash(value) {
  const encoder = new TextEncoder();
  const data = encoder.encode(value);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map(byte => byte.toString(16).padStart(2, '0')).join('');
  return hashHex.substring(0, 8);
} 