import { format, createLogger, transports } from 'winston';
import 'winston-daily-rotate-file';

const { combine, timestamp, splat, prettyPrint } = format;

const logFilePath = process.cwd() + '/logs';
// Enable exception handling when you create your logger.
const logger = createLogger({
  format: combine(
    timestamp({ format: 'MMM-DD-YYYY HH:mm:ss' }),
    splat(),
    prettyPrint()
  ),
  transports: [
    new transports.Console({
      format: format.combine(
        format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss',
        }),
        format.colorize({
          message: true,
          colors: { info: 'green', error: 'red' },
        }),
        format.simple(),
      ),
    }),
    // 保存到文件
    new transports.DailyRotateFile({
      dirname: logFilePath,
      // 日志文件名 %DATE% 会自动设置为当前日期
      filename: 'echo-relayer-%DATE%.info.log',
      // 日期格式
      datePattern: 'YYYY-MM-DD',
      // 压缩文档，用于定义是否对存档的日志文件进行 gzip 压缩 默认值 false
      zippedArchive: true,
      // 文件最大大小，可以是bytes、kb、mb、gb
      maxSize: '200m',
      // 最大文件数，可以是文件数也可以是天数，天数加单位"d"，
      maxFiles: '7d',
      // 格式定义，同winston
      format: format.combine(
        format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss',
        }),
        format.json(),
      ),
      // 日志等级，不设置所有日志将在同一个文件
      level: 'info',
    }),
    // 同上述方法，区分error日志和info日志，保存在不同文件，方便问题排查
    new transports.DailyRotateFile({
      dirname: logFilePath,
      filename: 'echo-relayer-%DATE%.error.log',
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '200m',
      maxFiles: '14d',
      format: format.combine(
        format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss',
        }),
        format.json(),
      ),
      level: 'error',
    }),
  ],
});

export default logger;
