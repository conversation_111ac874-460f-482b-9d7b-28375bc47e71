import express from 'express';
import 'express-async-errors';
import db from "./db.js";
import { 
  getChainAndToken<PERSON><PERSON>, 
  isCommitteeAptSingle, 
  isCommitteeB2Single,
} from './util.js';
import { isCommitteeSuiSingle } from '../sui/index.js';
import config from '../config.js';
import logger from './logger.js';
import { CHAINID_APT } from '../const.js';

let router = express.Router();

const CHAINID_B2 = 1;
const CHAINID_BTC = 2;
const APT_ADDR = 0;
const CHAINID_XBTC = 10000;

router.post('/relayer/save/transactions', async (req, res, next) => {

  const source_chain = req.body.source_chain;
  const from_address = req.body.from_address;
  const target_chain = req.body.target_chain;
  const target_address = req.body.target_address;
  const token_type = req.body.token_type;

  // TODO
  // 验证机制，不要让任何人都可以发
  // 或者限流
  // 需要研究为什么链上数据会抓不到，按理说不该存在这个机制
  const userAgent = req.headers['user-agent'];
  const isBrowser = /Mozilla/.test(userAgent);
  if (!isBrowser) {
    res.json(error(30, 'invalid user'));
    return;
  }

  let original_amount = req.body.original_amount;
  if (source_chain == CHAINID_B2) { original_amount = original_amount / 100000000 }
  if (source_chain == CHAINID_BTC) { original_amount = original_amount * 100; }
  if (source_chain == CHAINID_XBTC) { original_amount = original_amount * 100; }

  let amount = req.body.amount / 100000000;
  if (source_chain == CHAINID_B2) { amount = amount / 100000000 }
  if (source_chain == CHAINID_BTC) { amount = amount * 100; }
  if (source_chain == CHAINID_XBTC) { amount = amount * 100; }

  let fee = req.body.fee / 100000000;
  if (source_chain == CHAINID_B2) { fee = fee / 100000000 }
  if (source_chain == CHAINID_BTC) { fee = fee * 100; }
  if (source_chain == CHAINID_XBTC) { fee = fee * 100; }

  const event_type = req.body.event_type;
  const hash = req.body.hash;

  if (source_chain == undefined || source_chain === '') {
    res.json(error(30, 'source_chain is invalid'));
    return;
  }
  if (from_address == undefined || from_address === '') {
    res.json(error(30, 'from_address is invalid'));
    return;
  }
  if (target_chain == undefined || target_chain === '') {
    res.json(error(30, 'target_chain is invalid'));
    return;
  }
  if (target_address == undefined || target_address === '') {
    res.json(error(30, 'target_address is invalid'));
    return;
  }
  if (token_type == undefined || token_type === '') {
    res.json(error(30, 'token_type is invalid'));
    return;
  }
  if (original_amount == undefined || original_amount === '') {
    res.json(error(30, 'original_amount is invalid'));
    return;
  }
  if (amount == undefined || amount === '') {
    res.json(error(30, 'amount is invalid'));
    return;
  }
  if (fee == undefined || fee === '') {
    res.json(error(30, 'fee is invalid'));
    return;
  }
  if (event_type == undefined || event_type === '') {
    res.json(error(30, 'event_type is invalid'));
    return;
  }
  if (hash == undefined || hash === '') {
    res.json(error(30, 'hash is invalid'));
    return;
  }


  let ret = await db.insertBridgeEventRecord(hash,
    source_chain,
    from_address,
    target_address,
    target_chain,
    token_type,
    original_amount,
    amount,
    fee,
    0,
    event_type)

  if (ret) {
    res.json(ok());
  } else {
    res.json(error(500, ret));
  }
});

router.get('/relayer/transactions', async (req, res, next) => {
  if (req.query.account == undefined || req.query.account == '') {
    res.json(error(30, 'aptos account is invalid'));
    return;
  }
  let page = req.query.page == undefined ? 1 : +req.query.page;
  let page_size = 10; // default page size 10
  let direction = req.query.direction == 1 ? 1 : 2; // 1: b2->aptos, 2: aptos->b2

  // REV - 0 用APTOS地址， 1 用B2地址

  console.log(req.query.account)
  // TODO:
  // SQL语句优化
  // WITHDRAW部分也需要合并表
  if (req.query.rev == APT_ADDR) {
    res.json(ok(await db.getUserTransactions(req.query.account, page, page_size, direction)));
  }
  else {
    res.json(ok(await db.getUserRevTransactions(req.query.account, page, page_size, direction)));
  }

});

router.get('/relayer/vault/transactions', async (req, res) => {
  if (req.query.account == undefined || req.query.account == '') {
    res.json(error(30, 'account is invalid'));
    return;
  }
  let page = req.query.page == undefined ? 1 : +req.query.page;
  let page_size = 20; // default page size 20
  const targetChainId = req.query.target_chain_id == undefined ? undefined : +req.query.target_chain_id;

  const transactions = await db.getBridgeTransactions(req.query.account, targetChainId, page, page_size);
  res.json(ok(transactions));
});

router.get('/relayer/vault/history_transactions', async (req, res) => {
  const query = req.query;
  const fromAddress = query.from_address;
  const fromChainId = query.from_chain_id ? +query.from_chain_id : undefined;
  const page = query.page ? +query.page : 1;
  const pageSize = query.page_size ? +query.page_size : 10;

  // const targetAddress = query.to_address;
  // const toChainId = query.to_chain_id ? +query.to_chain_id : undefined;
  if (!fromAddress) {
    return res.json(error(30, 'from_address is required'));
  }

  if (fromChainId === undefined) {
    return res.json(error(30, 'from_chain_id is required'));
  }

  const transactions = await db.getVaultHistoryTransactions(fromAddress, fromChainId, page, pageSize);
  res.json(ok(transactions));
});

router.get('/relayer/withdraw/limit_amount', async (req, res) => {
  const query = req.query;

  const targetChain = parseInt(query.target_chain);
  const targetAddress = query.target_address;
  const tokenType = parseInt(query.token_type);
  const sourceChain = query.source_chain ? parseInt(query.source_chain): undefined; // 这里是发起链 aptos/movement/morphl
  const sourceAddress = query.source_address;
  if (config.superWhiteList.includes(sourceAddress)) { // 白名单
    return res.json(ok({
      limitAmount: Number.MAX_SAFE_INTEGER,
      loose: true,
    }));
  }


  const chainTokenKey = getChainAndTokenKey(tokenType);
  let withoutRevenue = true;
  if (config.checkLimitChainTokens.includes(chainTokenKey)) {
    withoutRevenue = false;
  }

  let limitAmount = await db.getWithdrawLimitAmount(targetAddress, targetChain, tokenType, sourceChain);

  // aptos -> b2
  if (sourceChain === CHAINID_APT && targetChain === CHAINID_B2) {
    const dayWithdrawAmount = await db.getDayWithdrawAmount(targetAddress, CHAINID_APT, CHAINID_B2);
    const restAmount = config.aptos_b2.dayLimit - dayWithdrawAmount;
    limitAmount = Math.min(restAmount, config.aptos_b2.singleLimit, limitAmount);
  }
  if (limitAmount < 0) {
    limitAmount = 0;
  }
  const data = {
    limitAmount,
    loose: withoutRevenue, // 是否宽松模式
  };
  res.json(ok(data));
});

router.get('/relayer/withdraw/aptos_b2/day_limit', async (req, res) => {
  const query = req.query;

  const targetAddress = query.address;
  if (!targetAddress) {
    res.json(error(30, 'address is invalid'));
    return;
  }

  const dayWithdrawAmount = await db.getDayWithdrawAmount(targetAddress, CHAINID_APT, CHAINID_B2);
  const restAmount = config.aptos_b2.dayLimit - dayWithdrawAmount;
  const data = {
    dayLimit: restAmount < 0 ? 0 : restAmount,
    singleLimit: config.aptos_b2.singleLimit,
  };
  res.json(ok(data));
});

router.post('/relayer/sign/transactions/inner_partner', async (req, res) => {
  const body = req.body;

  logger.info("sign transactions inner partner", body, req.hostname, req.ip, req.rawHeaders);
  try {
    const hash = body.hash;
    const signature = body.signature;
    const signChainName = body.signChainName;

    if (!hash || !signature || !signChainName) {
      return res.status(500).send({ error: "Missing required fields" });
    }
    switch (signChainName) {
      case 'sui':
      case 'aptos': {
        const event = await db.getBTCDepositEventByHash(hash);
        let verify = false;
        if (signChainName === 'aptos') {
          verify = await isCommitteeAptSingle(signature, event);
        } else if (signChainName === 'sui') {
          verify = await isCommitteeSuiSingle(signature, event);
        }
        if (verify) {
          let sig = [];
          if (event.sign != "0") { 
            sig = JSON.parse(event.sign); 
          }
          if (!sig.includes(signature)) {
            sig.push(signature);
            await db.updateBTCDepositEventSig(JSON.stringify(sig), hash);
            res.status(500).send({ error: "First attempt" });
          } else {
            res.json(ok("Successfully"));
          }
        } else {
          res.status(500).send({ error: "Invaild signature" });
        }
        break;
      }
      case 'b2':
      case 'evm':{ // 泛指类evm链
        const event = await db.getBTCWithdrawEventByHash(hash);
        let flag = await isCommitteeB2Single(signature, event);
        if (flag) {
          let sig = [];
          if (event.sign != "0") { 
            sig = JSON.parse(event.sign); 
          }
          if (!sig.includes(signature)) {
            sig.push(signature);
            await db.updateBTCWithdrawEventSig(JSON.stringify(sig), hash);
            res.status(500).send({ error: "First attempt" });
          } else {
            return res.json(ok("Successfully"));
          }
        } else {
          return res.status(500).send({ error: "Invaild signature" });
        }
        break;
      }
    }
  } catch(e) {
    logger.error("relayer sign", { exception: e?.stack || e?.message });
    return res.status(500).send({ error: "Unknown Error" });
  }
});

function ok(data) {
  return {
    code: 200,
    data: data
  }
}

function error(code, msg) {
  return {
    code: code,
    msg: msg
  }
}

export default router;
