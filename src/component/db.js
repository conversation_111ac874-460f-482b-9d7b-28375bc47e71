import config from "../config.js";
import mysql from "mysql";
import moment from "moment";
import logger from "./logger.js";
import { getChainAndTokenKey, getRealAptosAddress } from './util.js';

const MAX_LENGTH = 490

console.log("mysql.createPool");
const pool = mysql.createPool({
  connectionLimit: 100,
  host: config.db.host,
  port: config.db.port,
  user: config.db.user,
  password: config.db.password,
  database: config.db.database,
  multipleStatements: true,
  supportBigNumbers: true
});

function transaction(sqls, params) {
  return new Promise((resolve, reject) => {
    pool.getConnection(function (err, connection) {
      if (err) {
        return reject(err);
      }

      if (sqls.length !== params.length) {
        connection.release();
        return reject(new Error("statement and values unmatch"));
      }

      connection.beginTransaction((beginErr) => {
        if (beginErr) {
          connection.release();
          return reject(beginErr);
        }
        console.log("begin transaction: " + sqls.length + "statements");
        let funcAry = sqls.map((sql, index) => {
          return new Promise((sqlResolve, sqlReject) => {
            const data = params[index];
            connection.query(sql, data, (sqlErr, result) => {
              if (sqlErr) {
                return sqlReject(sqlErr);
              }
              sqlResolve(result);
            });
          });
        });

        Promise.all(funcAry)
          .then((arrResult) => {
            connection.commit(function (commitErr, info) {
              if (commitErr) {
                console.log("submit transaction failed:" + commitErr);
                connection.rollback(function (err) {
                  if (err) console.log("rollback failed:" + err);
                  connection.release();
                });
                return reject(commitErr);
              }

              connection.release();
              resolve(arrResult);
            });
          })
          .catch((error) => {
            connection.rollback(function () {
              console.log("sql execute failed:" + JSON.stringify(error));
              connection.release();
              reject(error);
            });
          });
      });
    });
  });
}

function queryOne(sqlString, params) {
  return new Promise(function (resolve, reject) {
    pool.query(sqlString, params, function (error, results, fields) {
      if (error) {
        console.log("sql execute failed:" + JSON.stringify(error));
        return reject(error);
      } else {
        if (results.length > 0) {
          let retobj = {};
          for (let i = 0; i < fields.length; ++i) {
            retobj[fields[i].name] = results[0][fields[i].name];
          }
          resolve(retobj);
        } else {
          resolve(null);
        }
      }
    });
  });
}

function queryArray(sqlString, params) {
  return new Promise(function (resolve, reject) {
    pool.query(sqlString, params, function (error, results, fields) {
      if (error) {
        console.log("sql execute failed:" + JSON.stringify(error));
        return reject(error);
      } else {
        let retArr = [];
        for (let ri = 0; ri < results.length; ++ri) {
          let retobj = {};
          for (let i = 0; i < fields.length; ++i) {
            retobj[fields[i].name] = results[ri][fields[i].name];
          }
          retArr.push(retobj);
        }

        resolve(retArr);
      }
    });
  });
}

async function initDatabase() {
  try {
    let sqls = [
      `CREATE TABLE IF NOT EXISTS Error(
        id INT UNSIGNED AUTO_INCREMENT,
        hash VARCHAR(100) UNIQUE NOT NULL,
        sequence_number VARCHAR(66) UNIQUE NOT NULL,
        timestamp BIGINT UNSIGNED NOT NULL,
        errortype VARCHAR(100) NOT NULL,
        state INT UNSIGNED DEFAULT 0,

        created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
     
        PRIMARY KEY ( id ),
        INDEX ( sequence_number, hash, timestamp )
      )
      ENGINE=InnoDB
      DEFAULT CHARSET=utf8mb4
      COLLATE=utf8mb4_unicode_ci;`,

      `CREATE TABLE IF NOT EXISTS BTCWithdrawEvent(
          id INT UNSIGNED AUTO_INCREMENT,
          hash VARCHAR(100) UNIQUE NOT NULL COMMENT '哈希值',
          sequence_number VARCHAR(66)  NOT NULL COMMENT 'Move链上的序号',
          source_chain BIGINT UNSIGNED NOT NULL COMMENT '原始链',
          from_address VARCHAR(66) NOT NULL COMMENT '原始地址',
          sin_address VARCHAR(66) NOT NULL COMMENT '新火地址',
          target_chain BIGINT UNSIGNED NOT NULL COMMENT '目标链',
          target_address VARCHAR(66) NOT NULL COMMENT '目标链地址',
          target_hash VARCHAR(100) COMMENT '跨链出去的hash, 这里是新火返回', 
          token_type BIGINT UNSIGNED NOT NULL COMMENT '资产ID',
          original_amount BIGINT UNSIGNED NOT NULL COMMENT '原本金额',
          amount BIGINT UNSIGNED NOT NULL COMMENT '扣除手续费金额',
          fee BIGINT UNSIGNED NOT NULL COMMENT '手续费比例',
          version BIGINT UNSIGNED NOT NULL COMMENT '版本',
          timestamp BIGINT UNSIGNED NOT NULL COMMENT 'Move链上的时间戳',
          sign VARCHAR(2000) NOT NULL COMMENT '保留签名字段',
          state INT UNSIGNED DEFAULT 0 COMMENT '0=等待执行 1=成功 2=失败 3=pending 4=黑名单冻结 5=白名单处理 6=异常 7=btc验证异常',
          try_count INT(10) NULL DEFAULT 0 COMMENT '重试次数',
          error_msg VARCHAR(500) NULL COMMENT '报错信息',
          proof VARCHAR(200) NULL,
          block_height INT NULL COMMENT '区块高度',
          platform_id INT(2) DEFAULT 0 COMMENT '平台id',

          created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

          PRIMARY KEY ( id ),
          INDEX ( target_chain, source_chain, from_address, target_address, sin_address, hash, sequence_number, version, timestamp, created, original_amount, amount, state, proof, target_hash )
      )
      ENGINE=InnoDB
      DEFAULT CHARSET=utf8mb4
      COLLATE=utf8mb4_unicode_ci;`,


      `CREATE TABLE IF NOT EXISTS bridgeblackwhitelist (
        id INT(10) UNSIGNED NOT NULL AUTO_INCREMENT,
        address VARCHAR(66) NOT NULL,
        state INT(2) NOT NULL DEFAULT 1 COMMENT '1=有效 2=无效',
        list_type INT(2) NOT NULL COMMENT '1=白名单 2=黑名单',
        chain_id INT(5) NULL COMMENT '链id 链id和地址匹配',

        created TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        INDEX ( address, list_type,chain_id  )

      )
      ENGINE=InnoDB
      DEFAULT CHARSET=utf8mb4
      COLLATE=utf8mb4_unicode_ci;`,

      `CREATE TABLE IF NOT EXISTS BTCDepositEvent(
          id INT UNSIGNED AUTO_INCREMENT,
          hash VARCHAR(100) UNIQUE NOT NULL COMMENT '哈希值, 即比特币上的txid',
          sequence_number VARCHAR(66) NOT NULL COMMENT '区块高度或者序列号',
          source_chain BIGINT UNSIGNED NOT NULL COMMENT '原始链',
          from_address VARCHAR(66) NOT NULL COMMENT '原始地址',
          sin_address VARCHAR(66) NOT NULL COMMENT '新火地址',
          target_chain BIGINT UNSIGNED NOT NULL COMMENT '目标链',
          target_address VARCHAR(66) NOT NULL COMMENT '目标链地址',
          target_hash VARCHAR(100) COMMENT '跨链出去的hash, 这里是新火返回', 
          token_type BIGINT UNSIGNED NOT NULL COMMENT '资产ID',
          original_amount BIGINT UNSIGNED NOT NULL COMMENT '原本金额',
          amount BIGINT UNSIGNED NOT NULL COMMENT '扣除手续费金额',
          fee BIGINT UNSIGNED NOT NULL COMMENT '手续费比例',
          version BIGINT UNSIGNED NOT NULL COMMENT '版本',
          timestamp BIGINT UNSIGNED NOT NULL COMMENT 'BTC链上的时间戳',
          sign VARCHAR(2000) NOT NULL COMMENT '保留签名字段',
          state INT UNSIGNED DEFAULT 0 COMMENT '0=等待执行 1=成功 2=失败 3=pending 4=黑名单冻结 5=白名单处理 6=异常 7=btc验证异常',
          try_count INT(10) NULL DEFAULT 0 COMMENT '重试次数',
          error_msg VARCHAR(500) NULL COMMENT '报错信息',
          proof VARCHAR(200) NULL,
          block_height INT NULL COMMENT '区块高度',
          platform_id INT(2) DEFAULT 0 COMMENT '平台id',

          created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

          PRIMARY KEY ( id ),
          INDEX (target_chain, source_chain, from_address, target_address, sin_address, hash, sequence_number, timestamp, created, original_amount, amount, state , proof,  target_hash)
      )
      ENGINE=InnoDB
      DEFAULT CHARSET=utf8mb4
      COLLATE=utf8mb4_unicode_ci;`,

      `CREATE TABLE IF NOT EXISTS sendmessageevent (
        id INT UNSIGNED AUTO_INCREMENT,
        content TEXT NULL COMMENT '内容',
        state INT(2) NULL COMMENT '状态 0=未发送 1=已发送',
        event_type INT(2) NULL COMMENT '事件类型。1=桥 2=xx 3=xx',
        msg_level INT(2) NULL COMMENT '等级 1=通知 2=警告 3=异常',
        msg_type INT(2) NULL COMMENT '消息类型 1=程序错误 2=风控事件告警',
        created TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        INDEX (state, event_type, msg_level, msg_type)
      )
      ENGINE=InnoDB
      DEFAULT CHARSET=utf8mb4
      COLLATE=utf8mb4_unicode_ci;`,


      `CREATE TABLE IF NOT EXISTS  BridgeEventRecord (
        id INT UNSIGNED AUTO_INCREMENT,
        hash VARCHAR(100) NOT NULL COMMENT '哈希值, 即比特币上的txid',
        source_chain BIGINT(20) UNSIGNED NOT NULL COMMENT '原始链',
        from_address VARCHAR(66) NOT NULL COMMENT '原始地址',
        target_chain BIGINT(20) UNSIGNED NOT NULL COMMENT '目标链',
        target_address VARCHAR(66) NOT NULL COMMENT '目标链地址',
        token_type BIGINT(20) UNSIGNED NOT NULL COMMENT '资产ID',
        original_amount BIGINT(20) UNSIGNED NOT NULL COMMENT '原本金额',
        amount BIGINT(20) UNSIGNED NOT NULL COMMENT '扣除手续费金额',
        fee BIGINT(20) UNSIGNED NOT NULL COMMENT '手续费比例',
        state INT(10) UNSIGNED NULL DEFAULT '0' COMMENT '0=等待执行 1=成功 2=失败 3=pending 4=黑名单冻结 5=白名单处理 6=异常 7=btc验证异常',
        created TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        event_type INT(2) NULL COMMENT '1=deposit 2=withdraw',
        
        PRIMARY KEY (id),
        INDEX ( target_chain, source_chain, from_address, target_address, hash, created, original_amount, amount, state)
        )
       ENGINE=InnoDB
      DEFAULT CHARSET=utf8mb4
      COLLATE=utf8mb4_unicode_ci;`,
    ];

    let params = [];
    for (let i = 0; i < sqls.length; i++) {
      params.push([]);
    }

    let sqlres = await transaction(sqls, params);
    logger.info("initDatabase", { sqlres: sqlres });
  } catch (e) {
    logger.error("initDatabase", { error: e });
    return null;
  }
}

async function insertSendMsg(
  content,
  event_type,
  msg_level,
  msg_type
) {
  let ret = false;
  try {
    const sql =
      "insert INTO sendmessageevent(content, state, event_type,msg_level,msg_type) values(?,?,?,?,?)";
    let sqlres = await queryOne(sql, [
      content,
      0,
      event_type,
      msg_level,
      msg_type
    ]);
    logger.info("insertSendMsg", { sqlres: sqlres });
    ret = true;
  } catch (e) {
    if (e.errno == 1062 || e.code == "ER_DUP_ENTRY") {
      // ER_DUP_ENTRY
      ret = true;
    }
    else { logger.info("insertSendMsg", { exception: e }); }
  }
  return ret;
}

async function insertError(
  hash,
  sequence_number,
  timestamp,
  errortype,
  state
) {
  let ret = false;
  try {
    const sql =
      "insert INTO Error( hash, sequence_number, timestamp, errortype, state values(?,?,?,?,?)";
    let sqlres = await queryOne(sql, [
      hash,
      sequence_number,
      timestamp,
      errortype,
      state
    ]);
    logger.info("insertError", { sqlres: sqlres });
    ret = true;
  } catch (e) {
    if (e.errno == 1062 || e.code == "ER_DUP_ENTRY") {
      // ER_DUP_ENTRY
      ret = true;
    }
    else { logger.info("insertError", { exception: e }); }
  }
  return ret;
}

async function selectB2Address(state) {
  try {
    const sql =
      "select from_address from BTCDepositEvent where state=? order by created";
    let sqlres = await queryArray(sql, [state]);
    logger.info("selectBTCDepositEvent", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("selectBTCDepositEvent", { exception: e });
  }
  return null;
}

async function selectWhitlistAddress(address) {
  try {
    const sql =
      "select eventtype, timestamp from aBTCJoinEvent where state=? AND address=? order by created";
    let sqlres = await queryArray(sql, [1, address]);
    logger.info("selectBTCWhitlistEvent", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("selectBTCWhitlistEvent", { exception: e });
  }
  return null;
}

async function insertBTCDepositEvent(
  hash,
  sequence_number,
  source_chain,
  from_address,
  target_address,
  sin_address,
  target_chain,
  target_hash,
  token_type,
  original_amount,
  amount,
  fee,
  version,
  timestamp,
  sign,
  state
) {
  let ret = false;
  try {
    const sql =
      "insert INTO BTCDepositEvent( hash, sequence_number, source_chain, from_address, target_address, sin_address, target_chain, target_hash, token_type, original_amount, amount, fee, version, timestamp, sign, state) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    let sqlres = await queryOne(sql, [
      hash, sequence_number, source_chain, from_address, target_address, sin_address, target_chain, target_hash, token_type, original_amount, amount, fee, version, timestamp, sign, state
    ]);
    logger.info("insertBTCDepositEvent", { sqlres: sqlres });
    ret = true;
  } catch (e) {
    if (e.errno == 1062 || e.code == "ER_DUP_ENTRY") {
      // ER_DUP_ENTRY
      ret = true;
    }
    else { logger.info("insertBTCDepositEvent", { exception: e }); }
  }
  return ret;
}

async function insertBTCWithdrawEvent(
  hash,
  sequence_number,
  source_chain,
  from_address,
  target_address,
  sin_address,
  target_chain,
  target_hash,
  token_type,
  original_amount,
  amount,
  fee,
  version,
  timestamp,
  sign,
  state
) {
  let ret = false;
  try {
    const sql =
      "insert INTO BTCWithdrawEvent(hash, sequence_number, source_chain, from_address, target_address, sin_address, target_chain, target_hash, token_type, original_amount, amount, fee, version, timestamp, sign, state) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
    let sqlres = await queryOne(sql, [
      hash,
      sequence_number,
      source_chain,
      from_address,
      target_address,
      sin_address,
      target_chain,
      target_hash,
      token_type,
      original_amount,
      amount,
      fee,
      version,
      timestamp,
      sign,
      state
    ]);
    logger.info("insertBTCWithdrawEvent", { sqlres: sqlres });
    ret = true;
  } catch (e) {
    if (e.errno == 1062 || e.code == "ER_DUP_ENTRY") {
      // ER_DUP_ENTRY
      ret = true;
    }
    else { logger.info("insertBTCWithdrawEvent", { exception: e }); }
  }

  return ret;
}

async function selectBTCWithdrawEvent(state, count, target_chain, source_chain) {
  try {
    const sql =
      "select id, state, proof,hash, sequence_number, source_chain, from_address, target_address, sin_address, target_chain, target_hash, token_type, original_amount, amount, fee, version, timestamp, sign, updated, created from BTCWithdrawEvent WHERE state=? AND source_chain=? AND target_chain=? order by timestamp asc limit ?";
    let sqlres = await queryArray(sql, [state, source_chain, target_chain, count]);
    logger.info("selectBTCWithdrawEvent", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("selectBTCWithdrawEvent", { exception: e });
  }
  return null;
}

async function selectBTCWithdrawEventByhash(hash, target_chain) {
  try {
    const sql =
      "select id, state, proof, hash, sequence_number, source_chain, from_address, target_address, sin_address, target_chain, target_hash, token_type, original_amount, amount, fee, version, timestamp, state, updated, created from BTCWithdrawEvent WHERE hash=? AND target_chain=?";
    let sqlres = await queryOne(sql, [hash, target_chain]);
    logger.info("selectBTCWithdrawEventByhash", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("selectBTCWithdrawEventByhash", { exception: e });
  }
  return null;
}

async function selectBTCWithdrawEventByhashstate(hash, state, source_chain, target_chain) {
  try {
    const sql =
      "select id, state, proof, hash, sequence_number, source_chain, from_address, target_address, sin_address, target_chain, target_hash, token_type, original_amount, amount, fee, version, timestamp, state, updated, created from BTCWithdrawEvent WHERE hash=? AND state=? AND source_chain=? AND target_chain=?";
    let sqlres = await queryOne(sql, [hash, state, source_chain, target_chain]);
    logger.info("selectBTCWithdrawEventByhash", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("selectBTCWithdrawEventByhash", { exception: e });
  }
  return null;
}

async function selectBTCWithdrawEventByAPTAddressState(from_address, state, source_chain, target_chain) {
  try {
    const sql =
      "select id, state, proof, hash, sequence_number, source_chain, from_address, target_address, sin_address, target_chain, target_hash, token_type, original_amount, amount, fee, version, timestamp, state, updated, created from BTCWithdrawEvent WHERE from_address=? AND state=? AND source_chain=? AND target_chain=?";
    let sqlres = await queryArray(sql, [from_address, state, source_chain, target_chain]);
    logger.info("selectBTCWithdrawEventByhash", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("selectBTCWithdrawEventByhash", { exception: e });
  }
  return null;
}

async function selectBTCWithdrawEventBSourceAddressState(target_address, state, source_chain, target_chain) {
  try {
    const sql =
      "select id, state, proof, hash, sequence_number, source_chain, from_address, target_address, sin_address, target_chain, target_hash, token_type, original_amount, amount, fee, version, timestamp, state, updated, created from BTCWithdrawEvent WHERE from_address=? AND state=? AND source_chain=? AND target_chain=?";
    let sqlres = await queryArray(sql, [target_address, state, source_chain, target_chain]);
    logger.info("selectBTCWithdrawEventByhash", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("selectBTCWithdrawEventByhash", { exception: e });
  }
  return null;
}

async function selectBTCWithdrawEventByAddressStateTime(from_address, state, source_chain, target_chain, timestamp) {
  try {
    const sql =
      `select id, state, proof, hash, sequence_number, source_chain, from_address, target_address, sin_address, target_chain, target_hash, token_type, original_amount, amount, fee, version, timestamp, state, updated, created from BTCWithdrawEvent WHERE hash=? AND state=? AND source_chain=? AND target_chain=? AND updated > ${timestamp}`;
    let sqlres = await queryArray(sql, [from_address, state, source_chain, target_chain]);
    logger.info("selectBTCWithdrawEventByhash", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("selectBTCWithdrawEventByhash", { exception: e });
  }
  return null;
}

async function selectBTCDepositEventByhashstate(hash, state, source_chain, target_chain) {
  try {
    const sql =
      "select id, state, proof, block_height, hash, sequence_number, source_chain, from_address, target_address, sin_address, target_chain, target_hash, token_type, original_amount, amount, fee, version, timestamp, sign, updated, created from BTCDepositEvent WHERE hash=? AND state=? AND source_chain=? AND target_chain=?";
    let sqlres = await queryArray(sql, [hash, state, source_chain, target_chain]);
    logger.info("selectBTCDepositEvent", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("selectBTCDepositEvent", { exception: e });
  }
  return null;
}

async function selectBTCDepositEventByAPTAddressState(target_address, state, source_chain, target_chain) {
  try {
    const sql =
      "select id, state, proof, block_height, hash, sequence_number, source_chain, from_address, target_address, sin_address, target_chain, target_hash, token_type, original_amount, amount, fee, version, timestamp, sign, updated, created from BTCDepositEvent WHERE target_address=? AND state=? AND source_chain=? AND target_chain=?";
    let sqlres = await queryArray(sql, [target_address, state, source_chain, target_chain]);
    logger.info("selectBTCDepositEvent", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("selectBTCDepositEvent", { exception: e });
  }
  return null;
}

async function selectBTCDepositEventBySourceAddressState(from_address, state, source_chain, target_chain) {
  try {
    const sql =
      "select id, state, proof, block_height, hash, sequence_number, source_chain, from_address, target_address, sin_address, target_chain, target_hash, token_type, original_amount, amount, fee, version, timestamp, sign, updated, created from BTCDepositEvent WHERE target_address=? AND state=? AND source_chain=? AND target_chain=?";
    let sqlres = await queryArray(sql, [from_address, state, source_chain, target_chain]);
    logger.info("selectBTCDepositEvent", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("selectBTCDepositEvent", { exception: e });
  }
  return null;
}

async function selectBTCDepositEventByAddressStateTime(target_address, state, source_chain, target_chain, timestamp) {
  try {
    const sql =
      `select id, state, proof, block_height, hash, sequence_number, source_chain, from_address, target_address, sin_address, target_chain, target_hash, token_type, original_amount, amount, fee, version, timestamp, sign, updated, created from BTCDepositEvent WHERE hash=? AND state=? AND source_chain=? AND target_chain=? AND updated > ${timestamp}`;
    let sqlres = await queryArray(sql, [target_address, state, source_chain, target_chain]);
    logger.info("selectBTCDepositEvent", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("selectBTCDepositEvent", { exception: e });
  }
  return null;
}


async function selectBTCDepositEventByhash(hash, target_chain) {
  try {
    const sql =
      "select id, state, proof, block_height, hash, sequence_number, source_chain, from_address, target_address, sin_address, target_chain, target_hash, token_type, original_amount, amount, fee, version, timestamp, sign, updated, created from BTCDepositEvent WHERE hash=? AND target_chain=?";
    let sqlres = await queryArray(sql, [hash, target_chain]);
    logger.info("selectBTCDepositEvent", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("selectBTCDepositEvent", { exception: e });
  }
  return null;
}

async function selectBTCDepositEvent(state, count, target_chain, source_chain) {
  try {
    const sql =
      "select id, state, proof, block_height, hash, sequence_number, source_chain, from_address, target_address, sin_address, target_chain, target_hash, token_type, original_amount, amount, fee, version, timestamp, sign, updated, created from BTCDepositEvent where state=? AND target_chain=? AND source_chain=? order by created asc limit ?";
    let sqlres = await queryArray(sql, [state, target_chain, source_chain, count]);
    logger.info("selectBTCDepositEvent", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("selectBTCDepositEvent", { exception: e });
  }
  return null;
}

async function getBTCDepositEventByHash(hash) {
  try {
    const sql = "select * from BTCDepositEvent where hash=?";
    let sqlres = await queryOne(sql, [hash]);
    logger.info("getBTCDepositEventByHash", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("getBTCDepositEventByHash", { exception: e });
  }

  return null;
}

async function getBTCWithdrawEventByHash(hash) {
  try {
    const sql = "select * from BTCWithdrawEvent where hash=?";
    let sqlres = await queryOne(sql, [hash]);
    logger.info("getBTCWithdrawEventByHash", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("getBTCWithdrawEventByHash", { exception: e });
  }

  return null;
}

async function updateBTCWithdrawEvent(state, hash) {
  try {
    const sql = "update BTCWithdrawEvent set state=? where hash=?";
    let sqlres = await queryOne(sql, [state, hash]);
    logger.info("updateBTCWithdrawEvent", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("updateBTCWithdrawEvent", { exception: e });
  }

  return null;
}

async function updateBTCWithdrawEventTargetHash(hash, target_hash) {
  try {
    const sql = "update BTCWithdrawEvent set target_hash=? where hash=?";
    let sqlres = await queryOne(sql, [target_hash, hash]);
    logger.info("updateBTCWithdrawEvent", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("updateBTCWithdrawEvent", { exception: e });
  }

  return null;
}

async function updateBTCWithdrawEventTargetHashAndState(hash, state, target_hash) {
  try {
    const sql = "update BTCWithdrawEvent set target_hash=?, state=? where hash=?";
    let sqlres = await queryOne(sql, [target_hash, state, hash]);
    logger.info("updateBTCWithdrawEventTargetHashAndState", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("updateBTCWithdrawEventTargetHashAndState", { exception: e });
  }

  return null;
}

async function updateBTCWithdrawEventSig(sign, hash) {
  try {
    const sql = "update BTCWithdrawEvent set sign=? where hash=?";
    let sqlres = await queryOne(sql, [sign, hash]);
    logger.info("updateBTCWithdrawEventSig", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("updateBTCWithdrawEventSigError", { exception: e });
  }

  return null;
}


async function updateBTCDepositEvent(state, hash) {
  try {
    const sql = "update BTCDepositEvent set state=? where hash=?";
    let sqlres = await queryOne(sql, [state, hash]);
    logger.info("updateBTCDepositEvent", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("updateBTCDepositEventError", { exception: e });
  }

  return null;
}


async function updateBTCDepositEventWithFail(state, hash, err) {
  try {
    const sql = "update BTCDepositEvent set state=?, error_msg =?  where hash=?";
    let error = err.substring(0, MAX_LENGTH)
    let sqlres = await queryOne(sql, [state, error, hash]);
    logger.info("updateBTCDepositEvent", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("updateBTCDepositEventError", { exception: e });
  }

  return null;
}

async function updateBTCWithdrawEventWithFail(state, hash, err) {
  try {
    const sql = "update BTCWithdrawEvent set state=?, error_msg =? where hash=?";
    let error = err.substring(0, MAX_LENGTH)
    let sqlres = await queryOne(sql, [state, error, hash]);
    logger.info("updateBTCWithdrawEvent", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("updateBTCWithdrawEventError", { exception: e });
  }

  return null;
}

async function updateBTCDepositEventTargetHash(hash, target_hash) {
  try {
    const sql = "update BTCDepositEvent set target_hash=? where hash=?";
    let sqlres = await queryOne(sql, [target_hash, hash]);
    logger.info("updateBTCDepositEvent", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("updateBTCDepositEventError", { exception: e });
  }

  return null;
}

async function updateBTCDepositEventTargetHashState(hash, target_hash, state) {
  try {
    const sql = "update BTCDepositEvent set target_hash=?, state=? where hash=?";
    let sqlres = await queryOne(sql, [target_hash, state, hash]);
    logger.info("updateBTCDepositEvent", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("updateBTCDepositEventError", { exception: e });
  }

  return null;
}

async function updateBTCDepositEventSig(sign, hash) {
  try {
    const sql = "update BTCDepositEvent set sign=? where hash=?";
    let sqlres = await queryOne(sql, [sign, hash]);
    logger.info("updateBTCDepositEventSig", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("updateBTCDepositEventSigError", { exception: e });
  }

  return null;
}

async function updateBTCWithdrawEventProof(hash, target_chain, proof) {
  try {
    const sql = "update BTCWithdrawEvent set proof=? where target_chain=? and hash=?";
    let sqlres = await queryOne(sql, [proof, target_chain, hash]);
    logger.info("updateBTCWithdrawEventProof", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("updateBTCWithdrawEventProof", { exception: e });
  }

  return null;
}

async function updateBTCDepositEventProof(hash, target_chain, proof) {
  try {
    const sql = "update BTCDepositEvent set proof=? where target_chain=? and hash=?";
    let sqlres = await queryOne(sql, [proof, target_chain, hash]);
    logger.info("updateBTCDepositEventProof", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("updateBTCDepositEventProofError", { exception: e });
  }

  return null;
}

async function updateBTCDepositEventProofAndState(hash, state, proof) {
  try {
    const sql = "update BTCDepositEvent set proof=?, state=? where hash=?";
    let sqlres = await queryOne(sql, [proof, state, hash]);
    logger.info("updateBTCDepositEventProofAndState", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("updateBTCDepositEventProofAndState", { exception: e });
  }

  return null;
}

async function updateBTCWithdrawEventProofAndState(hash, state, proof) {
  try {
    const sql = "update BTCWithdrawEvent set proof=?, state=? where hash=?";
    let sqlres = await queryOne(sql, [proof, state, hash]);
    logger.info("updateBTCWithdrawEventProofAndState", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("updateBTCWithdrawEventProofAndState", { exception: e });
  }

  return null;
}



async function getBridgeWithdrawEventRecord(accounts, start, limit) {
  try {
    // check accounts array
    if (!Array.isArray(accounts)) {
      accounts = [accounts];
    }
    const sql = `SELECT 
   ifnull(bde.source_chain, bgr.source_chain) source_chain,
   ifnull(bde.from_address, bgr.from_address) from_address,
   ifnull(bde.target_chain, bgr.target_chain) target_chain,
   ifnull(bde.target_address, bgr.target_address) target_address,
   ifnull(bde.token_type, bgr.token_type) token_type,
   ifnull(bde.original_amount, bgr.original_amount) original_amount,
   ifnull(bde.amount, bgr.amount) amount,
   ifnull(bde.fee, bgr.fee) fee,
   ifnull(bde.state, bgr.state) state,
   bgr.hash,
   bde.timestamp,
   (case when bde.target_hash LIKE '0x%'  then bde.target_hash  else  '' end) as target_hash,
   bgr.created
    FROM 
    bridgeeventrecord bgr
    LEFT JOIN 
    BTCWithdrawEvent  bde
    ON 
    bde.hash = bgr.hash
    
    where bgr.event_type = 2 and bgr.token_type != 10000 
    
   and bgr.from_address in (?)
   order by  bgr.created desc
   LIMIT ? OFFSET ?`;
    let sqlres = await queryArray(sql, [accounts, limit, start]);
    logger.info("getBridgeWithdrawEventRecord", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("getBridgeWithdrawEventRecord", { exception: e });
  }

  return null;
}

async function getBridgeDepositEventRecord(accounts, start, limit) {
  try {
    // check accounts array
    if (!Array.isArray(accounts)) {
      accounts = [accounts];
    }
    const sql = `SELECT 
   ifnull(bde.source_chain, bgr.source_chain) source_chain,
   ifnull(bde.from_address, bgr.from_address) from_address,
   ifnull(bde.target_chain, bgr.target_chain) target_chain,
   ifnull(bde.target_address, bgr.target_address) target_address,
   ifnull(bde.token_type, bgr.token_type) token_type,
   ifnull(bde.original_amount, bgr.original_amount) original_amount,
   ifnull(bde.amount, bgr.amount) amount,
   ifnull(bde.fee, bgr.fee) fee,
   ifnull(bde.state, bgr.state) state,
   (case when bde.target_hash LIKE '0x%'  then bde.target_hash  else  '' end) as target_hash,
   bgr.hash,
   bde.timestamp,
   bgr.created
    FROM 
    bridgeeventrecord bgr
    LEFT JOIN 
    BTCDepositEvent bde
    ON 
    bde.hash = bgr.hash
    
    where bgr.event_type = 1 and bgr.token_type != 10000 
    
    and bgr.target_address in (?)
    order by  bgr.created desc
   LIMIT ? OFFSET ?`;
    let sqlres = await queryArray(sql, [accounts, limit, start]);
    logger.info("getBridgeDepositEventRecord", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("getBridgeDepositEventRecord", { exception: e });
  }

  return null;
}

async function getBridgeDepositEventRecordRev(account, start, limit) {
  try {
    const sql = `SELECT 
   ifnull(bde.source_chain, bgr.source_chain) source_chain,
   ifnull(bde.from_address, bgr.from_address) from_address,
   ifnull(bde.target_chain, bgr.target_chain) target_chain,
   ifnull(bde.target_address, bgr.target_address) target_address,
   ifnull(bde.token_type, bgr.token_type) token_type,
   ifnull(bde.original_amount, bgr.original_amount) original_amount,
   ifnull(bde.amount, bgr.amount) amount,
   ifnull(bde.fee, bgr.fee) fee,
   ifnull(bde.state, bgr.state) state,
   (case when bde.target_hash LIKE '0x%'  then bde.target_hash  else  '' end) as target_hash,
   bgr.hash,
   bde.timestamp,
   bgr.created
    FROM 
    bridgeeventrecord bgr
    LEFT JOIN 
    BTCDepositEvent bde
    ON 
    bde.hash = bgr.hash
    
    where bgr.event_type = 1
    
    and bgr.from_address = ?
    order by bgr.created desc
   LIMIT ? OFFSET ?`;
    let sqlres = await queryArray(sql, [account, limit, start]);
    logger.info("getBridgeDepositEventRecord", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("getBridgeDepositEventRecord", { exception: e });
  }

  return null;
}

// ON (bde.nonce = abe.seq_num
//   AND bde.from_address = abe.from_address
//   AND bde.to_address = abe.to_address)

async function getBTCToAptosEvents(accounts, start, limit) {
  try {
    // check accounts array
    if (!Array.isArray(accounts)) {
      accounts = [accounts];
    }
    const sql = `SELECT 
      hash,
      original_amount,
      target_address,
      from_address,
      timestamp,
      source_chain,
      target_chain,
      token_type,
      state,
      target_hash,
      created
  FROM
      BTCDepositEvent
  WHERE
      target_address in (?)
  ORDER BY id DESC
  LIMIT ? OFFSET ?`;
    let sqlres = await queryArray(sql, [accounts, limit, start]);
    logger.info("getBTCAptosEvents", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("getBTCAptosEventsError", { exception: e });
  }

  return null;
}

async function getBTCToAptosEventsRev(account, start, limit) {
  try {
    const sql = `SELECT 
      hash,
      original_amount,
      target_address,
      from_address,
      timestamp,
      source_chain,
      target_chain,
      token_type,
      state,
      target_hash,
      created
  FROM
      BTCDepositEvent
  WHERE
      from_address = ?
  ORDER BY id DESC
  LIMIT ? OFFSET ?`;
    let sqlres = await queryArray(sql, [account, limit, start]);
    logger.info("getBTCAptosEvents", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("getBTCAptosEventsError", { exception: e });
  }

  return null;
}


async function getAptosToBTCEvents(accounts, start, limit) {
  try {
    if (!Array.isArray(accounts)) {
      accounts = [accounts];
    }
    const sql = `SELECT 
      hash,
      original_amount,
      target_address,
      from_address,
      timestamp,
      source_chain,
      target_chain,
      token_type,
      state,
      target_hash,
      created
  FROM
      BTCWithdrawEvent
  WHERE
      from_address in (?)
  ORDER BY id DESC
  LIMIT ? OFFSET ?`;
    let sqlres = await queryArray(sql, [accounts, limit, start]);
    logger.info("getBTCAptosEvents", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("getBTCAptosEventsError", { exception: e });
  }

  return null;
}

async function getAptosToBTCEventsRev(account, start, limit) {
  try {
    const sql = `SELECT 
      hash,
      original_amount,
      target_address,
      from_address,
      timestamp,
      source_chain,
      target_chain,
      token_type,
      state,
      target_hash,
      created
  FROM
      BTCWithdrawEvent
  WHERE
      target_address = ?
  ORDER BY id DESC
  LIMIT ? OFFSET ?`;
    let sqlres = await queryArray(sql, [account, limit, start]);
    logger.info("getBTCAptosEvents", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("getBTCAptosEventsError", { exception: e });
  }

  return null;
}

async function checkInBridgeList(address, chain_id, type) {
  try {
    const sql = "select address from bridgeblackwhitelist where state = 1 and list_type=? and chain_id = ? and address=?";
    let sqlres = await queryOne(sql, [type, chain_id, address]);
    logger.info("checkInBridgeList", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("checkInBridgeList", { exception: e });
  }

  return null;
}

async function insertBWlist(
  address,
  state,
  list_type,
  chain_id
) {
  let ret = false;
  try {
    const sql =
      "insert INTO bridgeblackwhitelist(address, state, list_type, chain_id) values(?,?,?,?) ";
    let sqlres = await queryOne(sql, [
      address,
      state,
      list_type,
      chain_id
    ]);
    logger.info("insertBridgeEventRecord", { sqlres: sqlres });
    ret = true;
  } catch (e) {
    if (e.errno == 1062 || e.code == "ER_DUP_ENTRY") {
      // ER_DUP_ENTRY
      ret = true;
    }
    else { logger.info("insertBridgeEventRecord", { exception: e }); }
  }

  return ret;
}

async function insertBridgeEventRecord(
  hash,
  source_chain,
  from_address,
  target_address,
  target_chain,
  token_type,
  original_amount,
  amount,
  fee,
  state,
  event_type
) {
  let ret = false;
  try {
    const sql =
      "insert INTO BridgeEventRecord(hash, source_chain, from_address, target_address, target_chain, token_type, original_amount, amount, fee, state, event_type) values(?,?,?,?,?,?,?,?,?,?,?) ";
    let sqlres = await queryOne(sql, [
      hash,
      source_chain,
      from_address,
      target_address,
      target_chain,
      token_type,
      original_amount,
      amount,
      fee,
      state,
      event_type
    ]);
    logger.info("insertBridgeEventRecord", { sqlres: sqlres });
    ret = true;
  } catch (e) {
    if (e.errno == 1062 || e.code == "ER_DUP_ENTRY") {
      // ER_DUP_ENTRY
      ret = true;
    }
    else { logger.info("insertBridgeEventRecord", { exception: e }); }
  }

  return ret;
}

async function getWithdrawLimitAmount(targetAddress, targetChain, tokenType, sourceChain) {
  let limitAmount = 0;
  try {
    const realAddress = getRealAptosAddress(targetAddress);
    const targetAddrresses = [targetAddress, realAddress];
    const chainTokenKey = getChainAndTokenKey(tokenType);
    let withRevenue = false;
    if (config.checkLimitChainTokens.includes(chainTokenKey)) {
      // return res.json(ok(Number.MAX_SAFE_INTEGER));
      withRevenue = true;
    }
    let depositSql = `SELECT sum(amount) as amount FROM BTCDepositEvent where state in (0,1,3) and source_chain = ? and token_type = ? `;
    let params = [targetChain, tokenType];
    if (withRevenue) {
      depositSql += ` and from_address in (?) `;
      params.push(targetAddrresses);
    }
    if (sourceChain !== undefined)  {
      depositSql += ` and target_chain = ? `;
      params.push(sourceChain);
    }
    logger.info("getWithdrawLimitAmount deposit sql", { sql: depositSql });
    let sqlres = await queryOne(depositSql, params);
    logger.info("getWithdrawLimitAmount deposit", { sqlres: sqlres });
    if (sqlres == null) {
      return 0;
    }
    const depositAmount = sqlres.amount;

    let withdrawSql = `SELECT sum(original_amount) as amount FROM BTCWithdrawEvent where state in (0,1,3,110) and target_chain = ? and token_type = ? `;
    let withdrawParams = [targetChain, tokenType];
    if (withRevenue) {
      withdrawSql += ` and target_address in (?) `;
      withdrawParams.push(targetAddrresses);
    }
    if (sourceChain !== undefined) {
      withdrawSql += ` and source_chain = ? `;
      withdrawParams.push(sourceChain);
    }
    sqlres = await queryOne(withdrawSql, withdrawParams);
    logger.info("getWithdrawLimitAmount withdraw", { sqlres: sqlres });
    const withdrawAmount = sqlres?.amount || 0 ;
    limitAmount = depositAmount - withdrawAmount;
  } catch (e) {
    logger.info("getWithdrawLimitAmount", { exception: e?.message });
  }
  return limitAmount;
};

async function getDayWithdrawAmount(address, sourceChain, targetChain) {
  let withdrawAmount = 0;
  try {
    const realAddress = getRealAptosAddress(address);
    const addressess = [address, realAddress];
    const startTimestmap = moment().startOf('day').unix() * 1000000;
    const endTimestamp = moment().add(1, 'days').startOf('day').unix() * 1000000;
    let withdrawSql = `SELECT sum(amount) as amount FROM BTCWithdrawEvent where state = 1 and from_address in (?) and source_chain = ? and target_chain = ? and timestamp >= ? and timestamp < ?`;
    let withdrawParams = [addressess, sourceChain, targetChain, startTimestmap, endTimestamp];
    
    const sqlres = await queryOne(withdrawSql, withdrawParams);
    logger.info("getDayWithdrawAmount withdraw", { sqlres: sqlres });

    withdrawAmount = sqlres?.amount || 0;
  } catch (e) {
    logger.info("getDayWithdrawAmount", { exception: e?.message });
  }
  return withdrawAmount;
};

async function loadDepositLastBlockNumber(sourceChainId) {
  try {
    // const sql = `SELECT sequence_number FROM BTCDepositEvent WHERE source_chain=? ORDER BY sequence_number DESC LIMIT 1`;
    const sql = `SELECT sequence_number FROM BTCDepositEvent WHERE source_chain=? ORDER BY id DESC LIMIT 1`;
    let sqlres = await queryOne(sql, [sourceChainId]);
    logger.info("loadDepositLastBlockNumber", { sqlres: sqlres });
    if (sqlres == null) {
      return 0;
    }
    return sqlres.sequence_number;
  } catch (e) {
    logger.error("loadDepositLastBlockNumber", { exception: e });
  }
  return 0;
}

async function loadWithdrawLastBlockNumber(sourceChainId) {
  try {
    // const sql = `SELECT sequence_number FROM BTCWithdrawEvent WHERE source_chain=? ORDER BY sequence_number DESC LIMIT 1`;
    const sql = `SELECT sequence_number FROM BTCWithdrawEvent WHERE source_chain=? ORDER BY id DESC LIMIT 1`;
    let sqlres = await queryOne(sql, [sourceChainId]);
    logger.info("loadWithdrawLastBlockNumber", { sqlres: sqlres });
    if (sqlres == null) {
      return 0;
    }
    return sqlres.sequence_number;
  } catch (e) {
    logger.error("loadWithdrawLastBlockNumber", { exception: e });
  }
  return 0;
}

async function loadSuiWithdrawLastCursor(chainId) {
  try {
    const sql = `SELECT hash, sequence_number FROM BTCWithdrawEvent WHERE source_chain=? ORDER BY id DESC LIMIT 1`;
    let sqlres = await queryOne(sql, [chainId]);
    logger.info("loadSuiWithdrawLastCursor", { sqlres: sqlres });
    if (sqlres == null) {
      return null;
    }
    return {
      eventSeq: sqlres.sequence_number,
      txDigest: sqlres.hash,
    };
  } catch (e) {
    logger.error("loadWithdrawLastBlockNumber", { exception: e });
  }
  return 0;
}

// async function getBridgeTransactions(account, page, page_size) {
async function getBridgeTransactions(account, targetChainId, page, page_size) {
  let start = (page - 1) * page_size;
  let limit = page_size;
  let aptosAccount = account;

  while (aptosAccount.length != 66 && aptosAccount.startsWith('0x')) {
    aptosAccount = aptosAccount.replace("0x", "0x0");
  }

  let depositTransactions = await getBTCToAptosEvents([aptosAccount, account], start, limit) || []; 
  depositTransactions = depositTransactions.concat(await getBridgeDepositEventRecord([aptosAccount, account], start, limit));
  depositTransactions = Array.from(new Map(depositTransactions.map(item => [item.hash, item])).values());
  depositTransactions = depositTransactions.sort((a, b) => +a.timestamp - (+b.timestamp));

  let withdrawTransactions = await getAptosToBTCEvents([aptosAccount, account], start, limit) || [];
  withdrawTransactions = withdrawTransactions.concat(await getBridgeWithdrawEventRecord([aptosAccount, account], start, limit));
  withdrawTransactions = Array.from(new Map(withdrawTransactions.map(item => [item.hash, item])).values());
  withdrawTransactions = withdrawTransactions.sort((a, b) => +a.timestamp - (+b.timestamp));

  if (targetChainId !== undefined) {
    depositTransactions = depositTransactions.filter(item => item.target_chain === targetChainId);
    withdrawTransactions = withdrawTransactions.filter(item => item.source_chain === targetChainId);
  }

  const res = {
    deposit: depositTransactions,
    withdraw: withdrawTransactions,
  };
  return res;
}

async function getVaultHistoryDepositEventRecord(fromAccounts, toAccounts) {
  try {
    const params = [];
    let sql = `SELECT 
   ifnull(bde.source_chain, bgr.source_chain) source_chain,
   ifnull(bde.from_address, bgr.from_address) from_address,
   ifnull(bde.target_chain, bgr.target_chain) target_chain,
   ifnull(bde.target_address, bgr.target_address) target_address,
   ifnull(bde.token_type, bgr.token_type) token_type,
   ifnull(bde.original_amount, bgr.original_amount) original_amount,
   ifnull(bde.amount, bgr.amount) amount,
   ifnull(bde.fee, bgr.fee) fee,
   ifnull(bde.state, bgr.state) state,
   (case when bde.state=1 then bde.target_hash  else  '' end) as target_hash,
   bgr.hash,
   bde.timestamp,
   bgr.created
    FROM 
    bridgeeventrecord bgr
    LEFT JOIN 
    BTCDepositEvent bde
    ON 
    bde.hash = bgr.hash
    where bgr.event_type = 1
    `;
    if (fromAccounts && fromAccounts.length > 0) {
      sql += ` and bgr.from_address in (?)`;
      params.push(fromAccounts);
    }
    if (toAccounts && toAccounts.length > 0) {
      sql += ` and bgr.target_address in (?)`;
      params.push(toAccounts);
    }
    sql += `
    order by  bgr.created desc
   `;
    // params.push(limit, start);
    let sqlres = await queryArray(sql, params);
    logger.info("getVaultHistoryDepositEventRecord", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("getVaultHistoryDepositEventRecord", { exception: e });
  }

  return null;
}

async function getVaultHistoryWithdrawEventRecord(fromAccounts, toAccounts) {
  try {
    const params = [];
    let sql = `SELECT 
   ifnull(bde.source_chain, bgr.source_chain) source_chain,
   ifnull(bde.from_address, bgr.from_address) from_address,
   ifnull(bde.target_chain, bgr.target_chain) target_chain,
   ifnull(bde.target_address, bgr.target_address) target_address,
   ifnull(bde.token_type, bgr.token_type) token_type,
   ifnull(bde.original_amount, bgr.original_amount) original_amount,
   ifnull(bde.amount, bgr.amount) amount,
   ifnull(bde.fee, bgr.fee) fee,
   ifnull(bde.state, bgr.state) state,
   bgr.hash,
   bde.timestamp,
   (case when bde.state=1  then bde.target_hash  else  '' end) as target_hash,
   bgr.created
    FROM 
    bridgeeventrecord bgr
    LEFT JOIN 
    BTCWithdrawEvent  bde
    ON 
    bde.hash = bgr.hash
    
    where bgr.event_type = 2
    `;
    if (fromAccounts && fromAccounts.length > 0) {
      sql += ` and bgr.from_address in (?)`;
      params.push(fromAccounts);
    }
    if (toAccounts && toAccounts.length > 0) {
      sql += ` and bgr.target_address in (?)`;
      params.push(toAccounts);
    }
    sql += `
   order by  bgr.created desc
   `;
    // params.push(limit, start);
    let sqlres = await queryArray(sql, params);
    logger.info("getVaultHistoryDepositEventRecord", { sqlres: sqlres });
    return sqlres;
  } catch (e) {
    logger.error("getVaultHistoryWithdrawEventRecord", { exception: e });
  }

  return null;
}

async function getVaultHistoryTransactions(fromAddress, fromChainId, page, pageSize) {
  const start = (page - 1) * pageSize;
  const limit = start + pageSize;
  let aptosFromAddress = fromAddress;
  // let aptosTargetAddress = targetAddress;
  
  // 兼容aptos
  while (fromAddress && aptosFromAddress.length != 66 && aptosFromAddress.startsWith('0x')) {
    aptosFromAddress = aptosFromAddress.replace("0x", "0x0");
  }
  // while (targetAddress && aptosTargetAddress.length != 66 && aptosTargetAddress.startsWith('0x')) {
  //   aptosTargetAddress = aptosTargetAddress.replace("0x", "0x0");
  // }

  let fromAccounts;
  // let toAccounts = [];
  if (fromAddress) {
    fromAccounts = [aptosFromAddress, fromAddress];
  }
  // if (targetAddress) {
  //   toAccounts = [aptosTargetAddress, targetAddress];
  // }

  let transactions = [];
  //deposit
  if (fromChainId === 4) {
    transactions = await getVaultHistoryDepositEventRecord(fromAccounts, []);
    transactions = transactions.concat(await getVaultHistoryWithdrawEventRecord([], fromAccounts));
  } else { //fromChainId = 16 withdraw  
    transactions = await getVaultHistoryDepositEventRecord([], fromAccounts);
    transactions = transactions.concat(await getVaultHistoryWithdrawEventRecord(fromAccounts, []));
  }
  transactions.sort((a, b) => +b.timestamp - (+a.timestamp));


  // let depositTransactions = await getBTCToAptosEvents([aptosAccount, account], start, limit) || []; 
  // depositTransactions = depositTransactions.concat(await getBridgeDepositEventRecord([aptosAccount, account], start, limit));
  // depositTransactions = Array.from(new Map(depositTransactions.map(item => [item.hash, item])).values());
  // depositTransactions = depositTransactions.sort((a, b) => +a.timestamp - (+b.timestamp));

  // let withdrawTransactions = await getAptosToBTCEvents([aptosAccount, account], start, limit) || [];
  // withdrawTransactions = withdrawTransactions.concat(await getBridgeWithdrawEventRecord([aptosAccount, account], start, limit));
  // withdrawTransactions = Array.from(new Map(withdrawTransactions.map(item => [item.hash, item])).values());
  // withdrawTransactions = withdrawTransactions.sort((a, b) => +a.timestamp - (+b.timestamp));

  return transactions.slice(start, limit);
}

export default {
  selectBTCDepositEventBySourceAddressState: selectBTCDepositEventBySourceAddressState,
  selectBTCDepositEventByAPTAddressState: selectBTCDepositEventByAPTAddressState,
  updateBTCWithdrawEventWithFail: updateBTCWithdrawEventWithFail,
  insertBWlist: insertBWlist,
  checkInBridgeList: checkInBridgeList,
  selectWhitlistAddress: selectWhitlistAddress,
  transaction: transaction,
  queryOne: queryOne,
  queryArray: queryArray,
  initDatabase: initDatabase,
  selectBTCWithdrawEventBSourceAddressState: selectBTCWithdrawEventBSourceAddressState,
  selectBTCWithdrawEventByAPTAddressState: selectBTCWithdrawEventByAPTAddressState,
  insertBTCDepositEvent: insertBTCDepositEvent,
  insertBTCWithdrawEvent: insertBTCWithdrawEvent,
  selectBTCWithdrawEvent: selectBTCWithdrawEvent,
  selectBTCWithdrawEventByhash: selectBTCWithdrawEventByhash,
  selectBTCDepositEvent: selectBTCDepositEvent,
  updateBTCWithdrawEvent: updateBTCWithdrawEvent,
  updateBTCWithdrawEventSig: updateBTCWithdrawEventSig,
  updateBTCDepositEvent: updateBTCDepositEvent,
  updateBTCDepositEventSig: updateBTCDepositEventSig,
  updateBTCDepositEventProof: updateBTCDepositEventProof,
  updateBTCWithdrawEventProof: updateBTCWithdrawEventProof,
  insertBridgeEventRecord: insertBridgeEventRecord,
  selectB2Address: selectB2Address,
  updateBTCWithdrawEventTargetHash: updateBTCWithdrawEventTargetHash,
  updateBTCWithdrawEventTargetHashAndState: updateBTCWithdrawEventTargetHashAndState,
  updateBTCDepositEventTargetHash: updateBTCDepositEventTargetHash,
  updateBTCDepositEventWithFail: updateBTCDepositEventWithFail,
  insertSendMsg: insertSendMsg,
  getBridgeDepositEventRecord: getBridgeDepositEventRecord,
  getBridgeWithdrawEventRecord: getBridgeWithdrawEventRecord,
  selectBTCWithdrawEventByhashstate: selectBTCWithdrawEventByhashstate,
  selectBTCDepositEventByhashstate: selectBTCDepositEventByhashstate,
  selectBTCDepositEventByhash: selectBTCDepositEventByhash,
  selectBTCWithdrawEventByAddressStateTime: selectBTCWithdrawEventByAddressStateTime,
  selectBTCDepositEventByAddressStateTime: selectBTCDepositEventByAddressStateTime,
  updateBTCDepositEventTargetHashState: updateBTCDepositEventTargetHashState,
  getBTCDepositEventByHash,
  getBTCWithdrawEventByHash,
  getWithdrawLimitAmount,
  getDayWithdrawAmount,
  loadDepositLastBlockNumber,
  loadWithdrawLastBlockNumber,
  loadSuiWithdrawLastCursor,
  getBridgeTransactions,
  getVaultHistoryTransactions,
  updateBTCDepositEventProofAndState,
  updateBTCWithdrawEventProofAndState,
  getUserTransactions: async function (account, page, page_size) {
    let start = (page - 1) * page_size;
    let limit = page_size;
    let res = {};
    let realaccount = account;
    console.log(realaccount);

    // 修复Aptos地址格式
    while (realaccount.length != 66 && realaccount.startsWith('0x')) {
      realaccount = realaccount.replace("0x", "0x0");
    }

    // 取出总表和临时表的event合并去重重排, 下同
    res.deposit = await getBTCToAptosEvents([realaccount, account], start, limit);
    res.deposit = res.deposit.concat(await getBridgeDepositEventRecord([realaccount, account], start, limit));
    res.deposit = Array.from(new Map(res.deposit.map(item => [item.hash, item])).values());
    res.deposit = res.deposit.sort((a, b) => new Date(a.created) - new Date(b.created));
    res.withdraw = await getAptosToBTCEvents([realaccount, account], start, limit);

    return res;
  },
  getUserRevTransactions: async function (account, page, page_size) {
    let start = (page - 1) * page_size;
    let limit = page_size;
    let res = {};
    let realaccount = account;

    // 取B2或者BTC地址，不需要额外处理

    res.deposit = await getBTCToAptosEventsRev(realaccount, start, limit);
    res.deposit = res.deposit.concat(await getBridgeDepositEventRecordRev(realaccount, start, limit))
    res.deposit = Array.from(new Map(res.deposit.map(item => [item.hash, item])).values());
    res.deposit = res.deposit.sort((a, b) => new Date(a.created) - new Date(b.created));
    res.withdraw = await getAptosToBTCEventsRev(realaccount, start, limit);
    return res;
  },
  loadB2DepositLastBlockNumber: async function () {
    try {
      // const sql = `SELECT sequence_number FROM BTCDepositEvent WHERE source_chain=1 ORDER BY sequence_number DESC LIMIT 1`;
      const sql = `SELECT sequence_number FROM BTCDepositEvent WHERE source_chain=1 ORDER BY id DESC LIMIT 1`;
      let sqlres = await queryOne(sql, []);
      logger.info("loadB2DepositLastBlockNumber", { sqlres: sqlres });
      if (sqlres == null) {
        return 0;
      }
      return sqlres.block_number;
    } catch (e) {
      logger.error("loadB2DepositLastBlockNumber", { exception: e });
    }

    return 0;
  },
  loadB2BridgeLastBlockNumber: async function () {
    try {
      const sql = `SELECT block_number FROM B2BridgeEvent ORDER BY block_number DESC LIMIT 1`;
      let sqlres = await queryOne(sql, []);
      logger.info("loadB2BridgeLastBlockNumber", { sqlres: sqlres });
      if (sqlres == null) {
        return 0;
      }
      return sqlres.block_number;
    } catch (e) {
      logger.error("loadB2BridgeLastBlockNumber", { exception: e });
    }

    return 0;
  },

  loadAptosWithdrawLastSequenceNumber: async function () {
    try {
      // const sql = `SELECT sequence_number FROM BTCWithdrawEvent WHERE source_chain=11 ORDER BY sequence_number DESC LIMIT 1`;
      const sql = `SELECT sequence_number FROM BTCWithdrawEvent WHERE source_chain=11 ORDER BY id DESC LIMIT 1`;
      let sqlres = await queryOne(sql, []);
      logger.info("loadAptosWithdrawLastSequenceNumber", { sqlres: sqlres });
      if (sqlres == null) {
        return 0;
      }
      return sqlres.sequence_number;
    } catch (e) {
      logger.error("loadAptosWithdrawLastSequenceNumber", { exception: e });
    }

    return 0;
  },

  loadMovementWithdrawLastSequenceNumber: async function () {
    try {
      // const sql = `SELECT sequence_number FROM BTCWithdrawEvent WHERE source_chain=12 ORDER BY sequence_number DESC LIMIT 1`;
      const sql = `SELECT sequence_number FROM BTCWithdrawEvent WHERE source_chain=12 ORDER BY id DESC LIMIT 1`;
      let sqlres = await queryOne(sql, []);
      logger.info("loadAptosWithdrawLastSequenceNumber", { sqlres: sqlres });
      if (sqlres == null) {
        return 0;
      }
      return sqlres.sequence_number;
    } catch (e) {
      logger.error("loadAptosWithdrawLastSequenceNumber", { exception: e });
    }

    return 0;
  },

  loadAptosBridgeLastSequenceNumber: async function () {
    try {
      // const sql = `SELECT sequence_number FROM AptosBridgeEvent ORDER BY sequence_number DESC LIMIT 1`;
      const sql = `SELECT sequence_number FROM AptosBridgeEvent ORDER BY id DESC LIMIT 1`;
      let sqlres = await queryOne(sql, []);
      logger.info("loadAptosBridgeLastSequenceNumber", { sqlres: sqlres });
      if (sqlres == null) {
        return 0;
      }
      return sqlres.sequence_number;
    } catch (e) {
      logger.error("loadAptosBridgeLastSequenceNumber", { exception: e });
    }

    return 0;
  },
};
