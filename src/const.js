export const CHAINID_B2 = 1;
export const CHAINID_BTC = 2;
export const CHAINID_APT = 11;
export const CHAINID_MOVE = 12;
export const CHAINID_MORPHL = 13;
export const CHAINID_SOLANA = 15;
export const CHAINID_SUI = 16;
export const CHAINID_IOTA = 17;

export const PATH_SIGNER = 1
export const PATH_SENDER = 2

export const EVENTTYPE_BRIDGE = 1
export const ERROR_LEVEL_INFO = 1
export const ERROR_LEVEL_WARNING = 2
export const ERROR_LEVEL_ALERT = 3
export const ERRORTYPE_INTERNAL = 1
export const ERRORTYPE_RISK = 2

export const MESSAGE_TYPE = 0;
export const VERSION = 1;

export const STATE_PENDING = 0
export const STATE_WHITE = 100
export const STATE_DONE = 1
export const STATE_FAILED = 2
export const STATE_REORG = 9
export const STATE_RISK = 8
export const STATE_BLACK = 4
export const STATE_BTCNOPROOF = 7
export const STATE_TOKEN_LIMIT = 110; // 用于指定币种跨入，那么只能跨出指定数量的币种
export const STATE_MANUAL_REVIEW = 111; // 需要人工审核

// export const B2_DEALALL = 1
// export const B2_ONLYAPT = 2
// export const B2_ONLYMOVE = 3
// export const B2_ONLY_MORPHL = 4
// export const ONLY_SEARCH_TARGET_EVM = 5;
export const SourceChainSupport = {
  B2_DEALALL: 1,
  B2_ONLYAPT: 2,
  B2_ONLYMOVE: 3,
  B2_ONLY_MORPHL: 4,
  ONLY_SEARCH_TARGET_EVM: 5,
  ONLY_SOLANA: 6,
  ONLY_TARGET_SUI: 7,
  ONLY_TARGET_IOTA: 8,
};

export const OVERWIRTE_ON = 1;


// evm sign message
export const SIGN_MESSAGE_TYPE = 0;
export const SIGN_VERSION = 1;
// 每执行LIMIT_LOOP_MAX次后，就需要处理一下因为转出待收益的token被限制的数据
export const LIMIT_LOOP_MAX = 100;
// 最大的区块跨度
export const MAX_BLOCK_SPAN = 1000;

// solana
export const SolanaMessageTypes = {
  TokenTransfer: 0,
  Blocklist: 1,
  EmergencyOp: 2,
  UpdateBridgeLimit: 3,
  UpdateTokenPrice: 4,
  Upgrade: 5,
  AddEvmTokens: 7,
  UpdateChainId: 8
};
// axios 的请求默认超时时间
export const AXIOS_DEFAULT_TIMEOUT = 60000;
