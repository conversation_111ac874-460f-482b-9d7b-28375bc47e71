{"abi": [{"type": "constructor", "inputs": [{"name": "_wBTC", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "getERC20Balance", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferERC20", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "recipient<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "wBTC", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IWBTC9"}], "stateMutability": "view"}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "AddressInsufficientBalance", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "853:2657:3:-:0;;;1189:134;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1224:10;;1269:95:20;;1322:31;;-1:-1:-1;;;1322:31:20;;1350:1;1322:31;;;455:51:78;428:18;;1322:31:20;;;;;;;1269:95;1373:32;1392:12;1373:18;:32::i;:::-;-1:-1:-1;1713:1:40;1917:21;;-1:-1:-1;;;;;1296:20:3::2;;::::0;853:2657;;2912:187:20;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:20;;;-1:-1:-1;;;;;;3020:17:20;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;14:290:78:-;84:6;137:2;125:9;116:7;112:23;108:32;105:52;;;153:1;150;143:12;105:52;179:16;;-1:-1:-1;;;;;224:31:78;;214:42;;204:70;;270:1;267;260:12;204:70;293:5;14:290;-1:-1:-1;;;14:290:78:o;309:203::-;853:2657:3;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "853:2657:3:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3047:10;-1:-1:-1;;;;;3069:4:3;3047:27;;3043:90;;3090:4;-1:-1:-1;;;;;3090:12:3;;3110:9;3090:32;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3043:90;853:2657;;;;;2293:101:20;;;;;;;;;;;;;:::i;1638:85::-;;;;;;;;;;-1:-1:-1;1684:7:20;1710:6;-1:-1:-1;;;;;1710:6:20;1638:85;;;-1:-1:-1;;;;;178:32:78;;;160:51;;148:2;133:18;1638:85:20;;;;;;;;971:28:3;;;;;;;;;;;;;;;1742:322;;;;;;;;;;-1:-1:-1;1742:322:3;;;;;:::i;:::-;;:::i;3360:148::-;;;;;;;;;;-1:-1:-1;3360:148:3;;;;;:::i;:::-;;:::i;:::-;;;1293:25:78;;;1281:2;1266:18;3360:148:3;1147:177:78;2543:215:20;;;;;;;;;;-1:-1:-1;2543:215:20;;;;;:::i;:::-;;:::i;2293:101::-;1531:13;:11;:13::i;:::-;2357:30:::1;2384:1;2357:18;:30::i;:::-;2293:101::o:0;1742:322:3:-;1531:13:20;:11;:13::i;:::-;2356:21:40::1;:19;:21::i;:::-;1987:70:3::2;2017:12;2032:16;2050:6;1987:22;:70::i;:::-;2398:20:40::1;1713:1:::0;2924:21;;2744:208;2398:20:::1;1742:322:3::0;;;:::o;3360:148::-;3456:45;;-1:-1:-1;;;3456:45:3;;3495:4;3456:45;;;160:51:78;3430:7:3;;-1:-1:-1;;;;;3456:30:3;;;;;133:18:78;;3456:45:3;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3449:52;3360:148;-1:-1:-1;;3360:148:3:o;2543:215:20:-;1531:13;:11;:13::i;:::-;-1:-1:-1;;;;;2627:22:20;::::1;2623:91;;2672:31;::::0;-1:-1:-1;;;2672:31:20;;2700:1:::1;2672:31;::::0;::::1;160:51:78::0;133:18;;2672:31:20::1;;;;;;;;2623:91;2723:28;2742:8;2723:18;:28::i;:::-;2543:215:::0;:::o;1796:162::-;1684:7;1710:6;-1:-1:-1;;;;;1710:6:20;735:10:39;1855:23:20;1851:101;;1901:40;;-1:-1:-1;;;1901:40:20;;735:10:39;1901:40:20;;;160:51:78;133:18;;1901:40:20;14:203:78;2912:187:20;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:20;;;-1:-1:-1;;;;;;3020:17:20;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;2431:307:40:-;1755:1;2558:7;;:18;2554:86;;2599:30;;-1:-1:-1;;;2599:30:40;;;;;;;;;;;2554:86;1755:1;2714:7;:17;2431:307::o;1303:160:37:-;1412:43;;;-1:-1:-1;;;;;1710:32:78;;;1412:43:37;;;1692:51:78;1759:18;;;;1752:34;;;1412:43:37;;;;;;;;;;1665:18:78;;;;1412:43:37;;;;;;;;-1:-1:-1;;;;;1412:43:37;-1:-1:-1;;;1412:43:37;;;1385:71;;1405:5;;1412:43;-1:-1:-1;;4504:33:37;;1427:14;;1412:43;4504:27;:33::i;:::-;4478:59;;4551:10;:17;4572:1;4551:22;;:57;;;;;4589:10;4578:30;;;;;;;;;;;;:::i;:::-;4577:31;4551:57;4547:135;;;4631:40;;-1:-1:-1;;;4631:40:37;;-1:-1:-1;;;;;178:32:78;;4631:40:37;;;160:51:78;133:18;;4631:40:37;14:203:78;2705:151:38;2780:12;2811:38;2833:6;2841:4;2847:1;2811:21;:38::i;:::-;2804:45;2705:151;-1:-1:-1;;;2705:151:38:o;3180:392::-;3279:12;3331:5;3307:21;:29;3303:108;;;3359:41;;-1:-1:-1;;;3359:41:38;;3394:4;3359:41;;;160:51:78;133:18;;3359:41:38;14:203:78;3303:108:38;3421:12;3435:23;3462:6;-1:-1:-1;;;;;3462:11:38;3481:5;3488:4;3462:31;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3420:73;;;;3510:55;3537:6;3545:7;3554:10;3510:26;:55::i;:::-;3503:62;3180:392;-1:-1:-1;;;;;;3180:392:38:o;4625:582::-;4769:12;4798:7;4793:408;;4821:19;4829:10;4821:7;:19::i;:::-;4793:408;;;5045:17;;:22;:49;;;;-1:-1:-1;;;;;;5071:18:38;;;:23;5045:49;5041:119;;;5121:24;;-1:-1:-1;;;5121:24:38;;-1:-1:-1;;;;;178:32:78;;5121:24:38;;;160:51:78;133:18;;5121:24:38;14:203:78;5041:119:38;-1:-1:-1;5180:10:38;5173:17;;5743:516;5874:17;;:21;5870:383;;6102:10;6096:17;6158:15;6145:10;6141:2;6137:19;6130:44;5870:383;6225:17;;-1:-1:-1;;;6225:17:38;;;;;;;;;;;445:173:78;513:20;;-1:-1:-1;;;;;562:31:78;;552:42;;542:70;;608:1;605;598:12;542:70;445:173;;;:::o;623:328::-;700:6;708;716;769:2;757:9;748:7;744:23;740:32;737:52;;;785:1;782;775:12;737:52;808:29;827:9;808:29;:::i;:::-;798:39;;856:38;890:2;879:9;875:18;856:38;:::i;:::-;846:48;;941:2;930:9;926:18;913:32;903:42;;623:328;;;;;:::o;956:186::-;1015:6;1068:2;1056:9;1047:7;1043:23;1039:32;1036:52;;;1084:1;1081;1074:12;1036:52;1107:29;1126:9;1107:29;:::i;1329:184::-;1399:6;1452:2;1440:9;1431:7;1427:23;1423:32;1420:52;;;1468:1;1465;1458:12;1420:52;-1:-1:-1;1491:16:78;;1329:184;-1:-1:-1;1329:184:78:o;1797:277::-;1864:6;1917:2;1905:9;1896:7;1892:23;1888:32;1885:52;;;1933:1;1930;1923:12;1885:52;1965:9;1959:16;2018:5;2011:13;2004:21;1997:5;1994:32;1984:60;;2040:1;2037;2030:12;2079:412;2208:3;2246:6;2240:13;2271:1;2281:129;2295:6;2292:1;2289:13;2281:129;;;2393:4;2377:14;;;2373:25;;2367:32;2354:11;;;2347:53;2310:12;2281:129;;;-1:-1:-1;2465:1:78;2429:16;;2454:13;;;-1:-1:-1;2429:16:78;2079:412;-1:-1:-1;2079:412:78:o", "linkReferences": {}, "immutableReferences": {"1678": [{"start": 105, "length": 32}, {"start": 144, "length": 32}, {"start": 360, "length": 32}]}}, "methodIdentifiers": {"getERC20Balance(address)": "b588d225", "owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "transferERC20(address,address,uint256)": "9db5dbe4", "transferOwnership(address)": "f2fde38b", "wBTC()": "9b452931"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.20+commit.a1b79de6\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_wBTC\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"AddressEmptyCode\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"AddressInsufficientBalance\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FailedInnerCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"}],\"name\":\"getERC20Balance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"recipientAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transferERC20\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"wBTC\",\"outputs\":[{\"internalType\":\"contract IWBTC9\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"details\":\"The contract is initialized with the deployer as the owner. The ownership is intended to be transferred to the EchoBridge contract after the bridge contract is deployed.\",\"errors\":{\"AddressEmptyCode(address)\":[{\"details\":\"There's no code at `target` (it is not a contract).\"}],\"AddressInsufficientBalance(address)\":[{\"details\":\"The ETH balance of the account is not enough to perform the operation.\"}],\"FailedInnerCall()\":[{\"details\":\"A call to an address target failed. The target may have reverted.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC20 token failed.\"}]},\"kind\":\"dev\",\"methods\":{\"constructor\":{\"params\":{\"_wBTC\":\"The address of the Wrapped Ether (WBTC) contract.\"}},\"getERC20Balance(address)\":{\"params\":{\"tokenAddress\":\"The address of the ERC20 token.\"},\"returns\":{\"_0\":\"The balance of the specified ERC20 token for this contract.\"}},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferERC20(address,address,uint256)\":{\"details\":\"This function is intended to only be called by the EchoBridge contract.\",\"params\":{\"amount\":\"The amount of tokens to transfer.\",\"recipientAddress\":\"The address to transfer the tokens to.\",\"tokenAddress\":\"The address of the ERC20 token.\"}},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"title\":\"BridgeVault\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"constructor\":{\"notice\":\"Constructor function for the BridgeVault contract.\"},\"getERC20Balance(address)\":{\"notice\":\"Gets the balance of a specified ERC20 token for this contract.\"},\"transferERC20(address,address,uint256)\":{\"notice\":\"Transfers ERC20 tokens from the contract to a target address. Only the owner of the contract can call this function.\"}},\"notice\":\"A contract that acts as a vault for transferring ERC20 tokens and BTC. It enables the owner (intended to be the EchoBridge contract) to transfer tokens to a target address. It also supports unwrapping WBTC (Wrapped Ether) and transferring the unwrapped BTC.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/BridgeVault.sol\":\"BridgeVault\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":@openzeppelin/openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":ds-test/=lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-foundry-upgrades/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"contracts/BridgeVault.sol\":{\"keccak256\":\"0x088b1a302e50f38ab6edffb89add82cb7edcae0cb605005db6b2dd5edd95409c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0180aebd0195860ccf7a7c977a671331f8e8f7f4580df6313ed956fc0e02c9bc\",\"dweb:/ipfs/QmVi4ZchrxSPhh2ZjxWejM8FGoDYJzW7vdeDhbwwmqdFHT\"]},\"contracts/interfaces/IBridgeVault.sol\":{\"keccak256\":\"0x5eb1f8e3a816e4a9367c742c5f9dbcd4dad8a6a2482a1cddccac6d7cbf11a76c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ee90e5ae689d3165f1aeb533b486f8149a68522c870645c97fc6ac4ce358dfe\",\"dweb:/ipfs/QmaqJA8RURAVZwdPdWEGx2DWW1Hohv4kWNE8nPCeDpVA4a\"]},\"contracts/interfaces/IWBTC9.sol\":{\"keccak256\":\"0x0c2dd4f49e64a47dd880f77468c51105952baaf1e92521dc890ff40bdc5443c7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f3bc8da3ac413af15a2b260a4ad8620650219cfb2c7ee3621b0d59566e3115df\",\"dweb:/ipfs/QmduV2o6WdpKKAw68urzBFueJwagfNoN3dPr1aRfAexVyd\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c\",\"dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d\",\"dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0\",\"dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245\",\"dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol\":{\"keccak256\":\"0xf980daa263b661ab8ddee7d4fd833c7da7e7995e2c359ff1f17e67e4112f2236\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7448ab095d6940130bcf76ba47a2eab14148c83119523b93dd89f6d84edd6c02\",\"dweb:/ipfs/QmawrZ4voKQjH3oomXT3Kuheb3Mnmo2VvVpxg8Ne5UJUrd\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.20+commit.a1b79de6"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_wBTC", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "type": "error", "name": "AddressEmptyCode"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "AddressInsufficientBalance"}, {"inputs": [], "type": "error", "name": "FailedInnerCall"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getERC20Balance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "address", "name": "recipient<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferERC20"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "wBTC", "outputs": [{"internalType": "contract IWBTC9", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "payable", "type": "receive"}], "devdoc": {"kind": "dev", "methods": {"constructor": {"params": {"_wBTC": "The address of the Wrapped Ether (WBTC) contract."}}, "getERC20Balance(address)": {"params": {"tokenAddress": "The address of the ERC20 token."}, "returns": {"_0": "The balance of the specified ERC20 token for this contract."}}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "transferERC20(address,address,uint256)": {"details": "This function is intended to only be called by the EchoBridge contract.", "params": {"amount": "The amount of tokens to transfer.", "recipientAddress": "The address to transfer the tokens to.", "tokenAddress": "The address of the ERC20 token."}}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"constructor": {"notice": "Constructor function for the BridgeVault contract."}, "getERC20Balance(address)": {"notice": "Gets the balance of a specified ERC20 token for this contract."}, "transferERC20(address,address,uint256)": {"notice": "Transfers ERC20 tokens from the contract to a target address. Only the owner of the contract can call this function."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "@openzeppelin/openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "ds-test/=lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-foundry-upgrades/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/BridgeVault.sol": "Bridge<PERSON>ault"}, "evmVersion": "paris", "libraries": {}}, "sources": {"contracts/BridgeVault.sol": {"keccak256": "0x088b1a302e50f38ab6edffb89add82cb7edcae0cb605005db6b2dd5edd95409c", "urls": ["bzz-raw://0180aebd0195860ccf7a7c977a671331f8e8f7f4580df6313ed956fc0e02c9bc", "dweb:/ipfs/QmVi4ZchrxSPhh2ZjxWejM8FGoDYJzW7vdeDhbwwmqdFHT"], "license": "MIT"}, "contracts/interfaces/IBridgeVault.sol": {"keccak256": "0x5eb1f8e3a816e4a9367c742c5f9dbcd4dad8a6a2482a1cddccac6d7cbf11a76c", "urls": ["bzz-raw://0ee90e5ae689d3165f1aeb533b486f8149a68522c870645c97fc6ac4ce358dfe", "dweb:/ipfs/QmaqJA8RURAVZwdPdWEGx2DWW1Hohv4kWNE8nPCeDpVA4a"], "license": "MIT"}, "contracts/interfaces/IWBTC9.sol": {"keccak256": "0x0c2dd4f49e64a47dd880f77468c51105952baaf1e92521dc890ff40bdc5443c7", "urls": ["bzz-raw://f3bc8da3ac413af15a2b260a4ad8620650219cfb2c7ee3621b0d59566e3115df", "dweb:/ipfs/QmduV2o6WdpKKAw68urzBFueJwagfNoN3dPr1aRfAexVyd"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70", "urls": ["bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c", "dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff", "urls": ["bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d", "dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386", "urls": ["bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0", "dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721", "urls": ["bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245", "dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol": {"keccak256": "0xf980daa263b661ab8ddee7d4fd833c7da7e7995e2c359ff1f17e67e4112f2236", "urls": ["bzz-raw://7448ab095d6940130bcf76ba47a2eab14148c83119523b93dd89f6d84edd6c02", "dweb:/ipfs/QmawrZ4voKQjH3oomXT3Kuheb3Mnmo2VvVpxg8Ne5UJUrd"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 4752, "contract": "contracts/BridgeVault.sol:BridgeVault", "label": "_owner", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 7018, "contract": "contracts/BridgeVault.sol:BridgeVault", "label": "_status", "offset": 0, "slot": "1", "type": "t_uint256"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "contracts/BridgeVault.sol", "id": 1766, "exportedSymbols": {"Address": [6977], "BridgeVault": [1765], "Context": [7007], "IBridgeVault": [2511], "IERC20": [6372], "IERC20Permit": [6434], "IWBTC9": [2568], "Ownable": [4891], "ReentrancyGuard": [7076], "SafeERC20": [6724]}, "nodeType": "SourceUnit", "src": "32:3479:3", "nodes": [{"id": 1662, "nodeType": "PragmaDirective", "src": "32:24:3", "nodes": [], "literals": ["solidity", "^", "0.8", ".20"]}, {"id": 1663, "nodeType": "ImportDirective", "src": "58:52:3", "nodes": [], "absolutePath": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol", "file": "@openzeppelin/contracts/access/Ownable.sol", "nameLocation": "-1:-1:-1", "scope": 1766, "sourceUnit": 4892, "symbolAliases": [], "unitAlias": ""}, {"id": 1664, "nodeType": "ImportDirective", "src": "111:56:3", "nodes": [], "absolutePath": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "file": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "nameLocation": "-1:-1:-1", "scope": 1766, "sourceUnit": 6373, "symbolAliases": [], "unitAlias": ""}, {"id": 1665, "nodeType": "ImportDirective", "src": "168:65:3", "nodes": [], "absolutePath": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "file": "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "nameLocation": "-1:-1:-1", "scope": 1766, "sourceUnit": 6725, "symbolAliases": [], "unitAlias": ""}, {"id": 1666, "nodeType": "ImportDirective", "src": "234:59:3", "nodes": [], "absolutePath": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol", "file": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "nameLocation": "-1:-1:-1", "scope": 1766, "sourceUnit": 7077, "symbolAliases": [], "unitAlias": ""}, {"id": 1667, "nodeType": "ImportDirective", "src": "294:39:3", "nodes": [], "absolutePath": "contracts/interfaces/IBridgeVault.sol", "file": "./interfaces/IBridgeVault.sol", "nameLocation": "-1:-1:-1", "scope": 1766, "sourceUnit": 2512, "symbolAliases": [], "unitAlias": ""}, {"id": 1668, "nodeType": "ImportDirective", "src": "334:33:3", "nodes": [], "absolutePath": "contracts/interfaces/IWBTC9.sol", "file": "./interfaces/IWBTC9.sol", "nameLocation": "-1:-1:-1", "scope": 1766, "sourceUnit": 2569, "symbolAliases": [], "unitAlias": ""}, {"id": 1765, "nodeType": "ContractDefinition", "src": "853:2657:3", "nodes": [{"id": 1678, "nodeType": "VariableDeclaration", "src": "971:28:3", "nodes": [], "constant": false, "functionSelector": "9b452931", "mutability": "immutable", "name": "wBTC", "nameLocation": "995:4:3", "scope": 1765, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_IWBTC9_$2568", "typeString": "contract IWBTC9"}, "typeName": {"id": 1677, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 1676, "name": "IWBTC9", "nameLocations": ["971:6:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 2568, "src": "971:6:3"}, "referencedDeclaration": 2568, "src": "971:6:3", "typeDescriptions": {"typeIdentifier": "t_contract$_IWBTC9_$2568", "typeString": "contract IWBTC9"}}, "visibility": "public"}, {"id": 1697, "nodeType": "FunctionDefinition", "src": "1189:134:3", "nodes": [], "body": {"id": 1696, "nodeType": "Block", "src": "1254:69:3", "nodes": [], "statements": [{"expression": {"id": 1694, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 1690, "name": "wBTC", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1678, "src": "1296:4:3", "typeDescriptions": {"typeIdentifier": "t_contract$_IWBTC9_$2568", "typeString": "contract IWBTC9"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 1692, "name": "_wBTC", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1681, "src": "1310:5:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 1691, "name": "IWBTC9", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2568, "src": "1303:6:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IWBTC9_$2568_$", "typeString": "type(contract IWBTC9)"}}, "id": 1693, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1303:13:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IWBTC9_$2568", "typeString": "contract IWBTC9"}}, "src": "1296:20:3", "typeDescriptions": {"typeIdentifier": "t_contract$_IWBTC9_$2568", "typeString": "contract IWBTC9"}}, "id": 1695, "nodeType": "ExpressionStatement", "src": "1296:20:3"}]}, "documentation": {"id": 1679, "nodeType": "StructuredDocumentation", "src": "1051:133:3", "text": "@notice Constructor function for the BridgeVault contract.\n @param _wBTC The address of the Wrapped Ether (WBTC) contract."}, "implemented": true, "kind": "constructor", "modifiers": [{"arguments": [{"expression": {"id": 1684, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "1224:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 1685, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1228:6:3", "memberName": "sender", "nodeType": "MemberAccess", "src": "1224:10:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "id": 1686, "kind": "baseConstructorSpecifier", "modifierName": {"id": 1683, "name": "Ownable", "nameLocations": ["1216:7:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 4891, "src": "1216:7:3"}, "nodeType": "ModifierInvocation", "src": "1216:19:3"}, {"arguments": [], "id": 1688, "kind": "baseConstructorSpecifier", "modifierName": {"id": 1687, "name": "Reentrancy<PERSON><PERSON>", "nameLocations": ["1236:15:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 7076, "src": "1236:15:3"}, "nodeType": "ModifierInvocation", "src": "1236:17:3"}], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 1682, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1681, "mutability": "mutable", "name": "_wBTC", "nameLocation": "1209:5:3", "nodeType": "VariableDeclaration", "scope": 1697, "src": "1201:13:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1680, "name": "address", "nodeType": "ElementaryTypeName", "src": "1201:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1200:15:3"}, "returnParameters": {"id": 1689, "nodeType": "ParameterList", "parameters": [], "src": "1254:0:3"}, "scope": 1765, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 1723, "nodeType": "FunctionDefinition", "src": "1742:322:3", "nodes": [], "body": {"id": 1722, "nodeType": "Block", "src": "1906:158:3", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 1716, "name": "tokenAddress", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1700, "src": "2017:12:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 1715, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6372, "src": "2010:6:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC20_$6372_$", "typeString": "type(contract IERC20)"}}, "id": 1717, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2010:20:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$6372", "typeString": "contract IERC20"}}, {"id": 1718, "name": "recipient<PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1702, "src": "2032:16:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 1719, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1704, "src": "2050:6:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_IERC20_$6372", "typeString": "contract IERC20"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 1712, "name": "SafeERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6724, "src": "1987:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_SafeERC20_$6724_$", "typeString": "type(library SafeERC20)"}}, "id": 1714, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1997:12:3", "memberName": "safeTransfer", "nodeType": "MemberAccess", "referencedDeclaration": 6484, "src": "1987:22:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_contract$_IERC20_$6372_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (contract IERC20,address,uint256)"}}, "id": 1720, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1987:70:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1721, "nodeType": "ExpressionStatement", "src": "1987:70:3"}]}, "baseFunctions": [2510], "documentation": {"id": 1698, "nodeType": "StructuredDocumentation", "src": "1329:408:3", "text": "@notice Transfers ERC20 tokens from the contract to a target address. Only the owner of\n the contract can call this function.\n @dev This function is intended to only be called by the EchoBridge contract.\n @param tokenAddress The address of the ERC20 token.\n @param recipientAddress The address to transfer the tokens to.\n @param amount The amount of tokens to transfer."}, "functionSelector": "9db5dbe4", "implemented": true, "kind": "function", "modifiers": [{"id": 1708, "kind": "modifierInvocation", "modifierName": {"id": 1707, "name": "only<PERSON><PERSON>er", "nameLocations": ["1871:9:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 4802, "src": "1871:9:3"}, "nodeType": "ModifierInvocation", "src": "1871:9:3"}, {"id": 1710, "kind": "modifierInvocation", "modifierName": {"id": 1709, "name": "nonReentrant", "nameLocations": ["1889:12:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 7040, "src": "1889:12:3"}, "nodeType": "ModifierInvocation", "src": "1889:12:3"}], "name": "transferERC20", "nameLocation": "1751:13:3", "overrides": {"id": 1706, "nodeType": "OverrideSpecifier", "overrides": [], "src": "1854:8:3"}, "parameters": {"id": 1705, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1700, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "1773:12:3", "nodeType": "VariableDeclaration", "scope": 1723, "src": "1765:20:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1699, "name": "address", "nodeType": "ElementaryTypeName", "src": "1765:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 1702, "mutability": "mutable", "name": "recipient<PERSON><PERSON><PERSON>", "nameLocation": "1795:16:3", "nodeType": "VariableDeclaration", "scope": 1723, "src": "1787:24:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1701, "name": "address", "nodeType": "ElementaryTypeName", "src": "1787:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 1704, "mutability": "mutable", "name": "amount", "nameLocation": "1821:6:3", "nodeType": "VariableDeclaration", "scope": 1723, "src": "1813:14:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1703, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1813:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1764:64:3"}, "returnParameters": {"id": 1711, "nodeType": "ParameterList", "parameters": [], "src": "1906:0:3"}, "scope": 1765, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 1745, "nodeType": "FunctionDefinition", "src": "3006:133:3", "nodes": [], "body": {"id": 1744, "nodeType": "Block", "src": "3033:106:3", "nodes": [], "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 1733, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 1727, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3047:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 1728, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3051:6:3", "memberName": "sender", "nodeType": "MemberAccess", "src": "3047:10:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [{"id": 1731, "name": "wBTC", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1678, "src": "3069:4:3", "typeDescriptions": {"typeIdentifier": "t_contract$_IWBTC9_$2568", "typeString": "contract IWBTC9"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_IWBTC9_$2568", "typeString": "contract IWBTC9"}], "id": 1730, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3061:7:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 1729, "name": "address", "nodeType": "ElementaryTypeName", "src": "3061:7:3", "typeDescriptions": {}}}, "id": 1732, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3061:13:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "3047:27:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1743, "nodeType": "IfStatement", "src": "3043:90:3", "trueBody": {"id": 1742, "nodeType": "Block", "src": "3076:57:3", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"argumentTypes": [], "expression": {"id": 1734, "name": "wBTC", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1678, "src": "3090:4:3", "typeDescriptions": {"typeIdentifier": "t_contract$_IWBTC9_$2568", "typeString": "contract IWBTC9"}}, "id": 1736, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3095:7:3", "memberName": "deposit", "nodeType": "MemberAccess", "referencedDeclaration": 2561, "src": "3090:12:3", "typeDescriptions": {"typeIdentifier": "t_function_external_payable$__$returns$__$", "typeString": "function () payable external"}}, "id": 1739, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "names": ["value"], "nodeType": "FunctionCallOptions", "options": [{"expression": {"id": 1737, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3110:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 1738, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3114:5:3", "memberName": "value", "nodeType": "MemberAccess", "src": "3110:9:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "src": "3090:30:3", "typeDescriptions": {"typeIdentifier": "t_function_external_payable$__$returns$__$value", "typeString": "function () payable external"}}, "id": 1740, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3090:32:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1741, "nodeType": "ExpressionStatement", "src": "3090:32:3"}]}}]}, "documentation": {"id": 1724, "nodeType": "StructuredDocumentation", "src": "2885:116:3", "text": "@notice Wraps as eth sent to this contract.\n @dev skip if sender is wBTC contract to avoid infinite loop."}, "implemented": true, "kind": "receive", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 1725, "nodeType": "ParameterList", "parameters": [], "src": "3013:2:3"}, "returnParameters": {"id": 1726, "nodeType": "ParameterList", "parameters": [], "src": "3033:0:3"}, "scope": 1765, "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"id": 1764, "nodeType": "FunctionDefinition", "src": "3360:148:3", "nodes": [], "body": {"id": 1763, "nodeType": "Block", "src": "3439:69:3", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 1759, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "3495:4:3", "typeDescriptions": {"typeIdentifier": "t_contract$_BridgeVault_$1765", "typeString": "contract BridgeVault"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_BridgeVault_$1765", "typeString": "contract BridgeVault"}], "id": 1758, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3487:7:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 1757, "name": "address", "nodeType": "ElementaryTypeName", "src": "3487:7:3", "typeDescriptions": {}}}, "id": 1760, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3487:13:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"arguments": [{"id": 1754, "name": "tokenAddress", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1748, "src": "3463:12:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 1753, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6372, "src": "3456:6:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC20_$6372_$", "typeString": "type(contract IERC20)"}}, "id": 1755, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3456:20:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$6372", "typeString": "contract IERC20"}}, "id": 1756, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3477:9:3", "memberName": "balanceOf", "nodeType": "MemberAccess", "referencedDeclaration": 6329, "src": "3456:30:3", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_address_$returns$_t_uint256_$", "typeString": "function (address) view external returns (uint256)"}}, "id": 1761, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3456:45:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 1752, "id": 1762, "nodeType": "Return", "src": "3449:52:3"}]}, "documentation": {"id": 1746, "nodeType": "StructuredDocumentation", "src": "3145:210:3", "text": "@notice Gets the balance of a specified ERC20 token for this contract.\n @param tokenAddress The address of the ERC20 token.\n @return The balance of the specified ERC20 token for this contract."}, "functionSelector": "b588d225", "implemented": true, "kind": "function", "modifiers": [], "name": "getERC20Balance", "nameLocation": "3369:15:3", "parameters": {"id": 1749, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1748, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "3393:12:3", "nodeType": "VariableDeclaration", "scope": 1764, "src": "3385:20:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1747, "name": "address", "nodeType": "ElementaryTypeName", "src": "3385:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "3384:22:3"}, "returnParameters": {"id": 1752, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1751, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 1764, "src": "3430:7:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1750, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3430:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3429:9:3"}, "scope": 1765, "stateMutability": "view", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [{"baseName": {"id": 1670, "name": "Ownable", "nameLocations": ["877:7:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 4891, "src": "877:7:3"}, "id": 1671, "nodeType": "InheritanceSpecifier", "src": "877:7:3"}, {"baseName": {"id": 1672, "name": "IBridgeVault", "nameLocations": ["886:12:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 2511, "src": "886:12:3"}, "id": 1673, "nodeType": "InheritanceSpecifier", "src": "886:12:3"}, {"baseName": {"id": 1674, "name": "Reentrancy<PERSON><PERSON>", "nameLocations": ["900:15:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 7076, "src": "900:15:3"}, "id": 1675, "nodeType": "InheritanceSpecifier", "src": "900:15:3"}], "canonicalName": "Bridge<PERSON>ault", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 1669, "nodeType": "StructuredDocumentation", "src": "369:484:3", "text": "@title BridgeVault\n @notice A contract that acts as a vault for transferring ERC20 tokens and BTC. It enables the owner\n (intended to be the EchoBridge contract) to transfer tokens to a target address. It also supports\n unwrapping WBTC (Wrapped Ether) and transferring the unwrapped BTC.\n @dev The contract is initialized with the deployer as the owner. The ownership is intended to be\n transferred to the EchoBridge contract after the bridge contract is deployed."}, "fullyImplemented": true, "linearizedBaseContracts": [1765, 7076, 2511, 4891, 7007], "name": "Bridge<PERSON>ault", "nameLocation": "862:11:3", "scope": 1766, "usedErrors": [4757, 4762, 6451, 6732, 6737, 6740, 7021], "usedEvents": [4768]}], "license": "MIT"}, "id": 3}