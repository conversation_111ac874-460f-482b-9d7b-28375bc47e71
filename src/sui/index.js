/* eslint-disable no-undef */
import { SuiClient, getFullnodeUrl } from "@mysten/sui/client";
import { Ed25519Keypair } from "@mysten/sui/keypairs/ed25519";
import { Transaction } from "@mysten/sui/transactions";
import { bcs, fromHex } from '@mysten/bcs';
import { ethers } from "ethers";
import axios from "axios";

import config from "../config.js";
import logger from "../component/logger.js";
import db from "../component/db.js";
import util from "../component/util.js";
import { setSuiCursor, getSuiCursor, getHash} from "../component/util.js";
import { sendNotification, sendHtml } from "../network/network_informer.js";
// import { message_sign_sol, message_encode_sol, message_payload_encode_sol, is_committee_sui } from "../component/util.js";
import { prerisk, afterrisk } from "../risk.js";
import { OVERWIRTE_ON, STATE_WHITE, STATE_PENDING, SIGN_MESSAGE_TYPE, SIGN_VERSION, PATH_SIGNER, PATH_SENDER, STATE_DONE, AXIOS_DEFAULT_TIMEOUT } from "../const.js";

export async function signSui(overwirte, pk, transferSignature) {
  logger.info("sui signer start");
  if (!config.sui.chainId) {
    throw new Error("target_sui.chainId is not set");
  }
  const targetChainId = config.sui.chainId;

  let mykey = "";
  if (overwirte == OVERWIRTE_ON) {
    mykey = pk;
  } else {
    mykey = config.sui.recoveryPhrase;
  }

  try {
    const ethChainId = config.ethereum.chainId;
    while (true) {
      try {
        let events = [];
        if (ethChainId) {
          events = events.concat(await db.selectBTCDepositEvent(STATE_PENDING, 5, targetChainId, ethChainId));
          events = events.concat(await db.selectBTCDepositEvent(STATE_WHITE, 5, targetChainId, ethChainId));
        }

        for (let index = 0; index < events.length; index++) {
          const ev = events[index];
          try {
            if (await prerisk(ev, ev.target_chain, PATH_SIGNER)) { 
              continue; 
            }
            let sig = [];
            if (ev.sign != "0") { 
              sig = JSON.parse(ev.sign); 
            }

            const serializeMessage = generateMessage(ev);
            const mysig = messageSign(serializeMessage, mykey);
            if (!sig.includes(mysig)) {
              sig.push(mysig);
              await db.updateBTCDepositEventSig(JSON.stringify(sig), ev.hash);
            }
            if (transferSignature) {
              const body = {
                hash: ev.hash,
                signature: mysig,
                signChainName: 'sui',
              };
              logger.info("submit sui to sender", body);
              await axios.post(config.signatureUpdateUrl, body, {timeout: AXIOS_DEFAULT_TIMEOUT}).then(async response => {
                  console.log('submit sui Response:', response.data);
                  await db.updateBTCDepositEvent(1, ev.hash);
              }).catch(async error => {
                  console.error('submit sui Error:', error?.message);
                  await db.updateBTCDepositEvent(0, ev.hash);
                });
            }
          } catch (e) {
            logger.error("sui singer event", { exception: e?.message });
          }
        }
      } catch (e) {
        logger.error("sui singer event", { exception: e?.message });
      }
      await util.delay(config.interval); // 1 second}
    }
  } catch (e) {
    logger.error("sui singer event", { exception: e?.stack || e?.message });
    await sendNotification("Warning: Sui Signer is Shutdown!", e?.stack || e?.message);
  }
  logger.info("sui singer end");
}


export async function submitSui(overwirte, pk) {
  logger.info("submitSui start");
  if (!config.sui.chainId) {
    throw new Error("target_sui.chainId is not set");
  }
  const targetChainId = config.sui.chainId;

  try {
    const client = new SuiClient({
      url: config.sui.rpcurl || getFullnodeUrl(config.sui.network),
    });

    // var b2Provider = new ethers.JsonRpcProvider(config.b2.url);

    let keypair;
    if (overwirte == OVERWIRTE_ON) {
      keypair = Ed25519Keypair.fromSecretKey(pk);
    } else {
      keypair = Ed25519Keypair.fromSecretKey(config.sui.recoveryPhrase);
    }
    const ethChainId = config.ethereum.chainId;
    while (true) {
      try {
        let events = [];
        if (ethChainId) {
          events = events.concat(await db.selectBTCDepositEvent(STATE_PENDING, 5, targetChainId, ethChainId));
          events = events.concat(await db.selectBTCDepositEvent(STATE_WHITE, 5, targetChainId, ethChainId));
        }

        for (let index = 0; index < events.length; index++) {
          const ev = events[index];

          if (ev.sign != "0") {
            const signatures = JSON.parse(ev.sign);
            if (signatures.length > config.threshold) {

              let res = await isCommitteeSui(signatures, ev);
              if (!res) { 
                continue;
              }
              // 风控
              if (await prerisk(ev, ev.target_chain, PATH_SENDER)) { continue; }

              try {
                await db.updateBTCDepositEvent(3, ev.hash);
                const tx = new Transaction();
                // const bridgeMessage = {
                //   message_type: SIGN_MESSAGE_TYPE,
                //   message_version: SIGN_VERSION,
                //   seq_num: BigInt(ev.sequence_number),
                //   source_chain: ev.source_chain,
                //   payload: [0x01, 0x02, 0x03],
                // };
                // const signatures = sig;

                // const serializedBridgeMessage = bcs
                //   .struct(`${config.sui.packageId}::bridge::BridgeMessage`)
                //   .serialize(bridgeMessage)
                //   .toBytes();
                const [ message ] = tx.moveCall({
                  target: `${config.sui.packageId}::message::create_token_bridge_message`,
                  arguments: [
                    tx.pure.u8(ev.source_chain),
                    tx.pure.u64(Number(ev.sequence_number)),
                    tx.pure(bcs.vector(bcs.u8()).serialize(fromHex(ev.from_address))),
                    tx.pure.u8(ev.target_chain),
                    tx.pure(bcs.vector(bcs.u8()).serialize(fromHex(ev.target_address))),
                    tx.pure.u8(ev.token_type),
                    tx.pure.u64(+ev.amount),
                  ],
                });

                tx.moveCall({
                  target: `${config.sui.packageId}::bridge::approve_token_transfer`,
                  arguments: [
                    tx.object(config.sui.bridgeObjectId),
                    message,
                    bcs.vector(bcs.vector(bcs.u8())).serialize(signatures.map(s => fromHex(s))),
                  ],
                });

                tx.moveCall({
                  target: `${config.sui.packageId}::bridge::claim_and_transfer_token`,
                  typeArguments: [config.sui.sbtcCoinType],
                  arguments: [
                    tx.object(config.sui.bridgeObjectId),
                    tx.object('0x6'),
                    tx.pure.u8(ev.source_chain),
                    tx.pure.u64(Number(ev.sequence_number)),
                  ],
                });
                
                // tx.moveCall({
                //   target: `${config.sui.packageId}::bridge::approve_token_transfer`,
                //   arguments: [
                //     tx.object(config.sui.bridgeObjectId),
                //     tx.pure(serializedBridgeMessage),
                //     tx.pure.vector("vector<u8>", signatures),
                //   ],
                // });
              
                // // 调用 claim_and_transfer_token
                // tx.moveCall({
                //   target: `${config.sui.packageId}::bridge::claim_and_transfer_token`,
                //   typeArguments: ["0x2::sui::SUI"],
                //   arguments: [
                //     tx.object(config.sui.bridgeObjectId),
                //     tx.object("0x6"),
                //     tx.pure.u8(ev.source_chain),
                //     tx.pure.u64(ev.sequence_number),
                //   ],
                // });

                // await client.devInspectTransactionBlock({
                //   transactionBlock: tx,
                //   sender: keypair.getPublicKey().toSuiAddress(),
                // });

                const result = await client.signAndExecuteTransaction({
                  transaction: tx,
                  signer: keypair,
                });
                await db.updateBTCDepositEventTargetHashState(ev.hash, result.digest, STATE_DONE);
                await afterrisk(ev);
              } catch (e) {
                let error = "Warning: Sui Signer Failed to sign transaction at" + ev.sequence_number + " ";
                logger.error("Sui withdraw failed!", { event: ev, exception: e?.message });
                if (e && e.message) {
                  error = e.message;
                }
                await db.updateBTCDepositEventWithFail(2, ev.hash, e.message);
                await sendHtml(ev.hash, ev.sequence_number, "", error, e);
              }
            }
          }
        }
      } catch (e) {
        await sendNotification("Warning: Sui Submitter Meet Problem!", e);
        logger.error("submitSui", { exception: e?.stack || e?.message });
      }
      await util.delay(config.interval); // 1 second
    }
  } catch (e) {
    await sendNotification("Warning: Sui Submitter is Shutdown!", e);
    logger.error("submitSui", { exception: e?.stack || e?.message });
  }
  logger.info("submitSui end");
}

export async function monitorEventSui() {
  logger.info("monitorEventSui start");
  if (!config.sui.chainId) {
    throw new Error("target_sui.chainId is not set");
  }
  // const targetChainId = config.sui.chainId;

  try {
    const rpcUrl = config.sui.rpcurl || getFullnodeUrl(config.sui.network);
    const client = new SuiClient({
      url: rpcUrl,
    });

    // let lastCursor = await db.loadSuiWithdrawLastCursor(targetChainId) || null;
    let lastCursor = await getSuiCursor() || null;
    if (lastCursor) {
      lastCursor = JSON.parse(lastCursor);
    }
    while (true) {
      try {
        const currentCursor = await getWithdrawSui(client, lastCursor);
        lastCursor = currentCursor;
      } catch (e) {
        logger.error("monitorEventSui", { exception: e?.stack || e?.message });
      }
      await util.delay(config.interval); // 1 second
    }
  } catch (e) {
    logger.error("sui Monitor Shutdown ", { exception: e?.message });
    await sendNotification("Warning: sui Monitor is Shutdown!", e);
  }
  logger.info("monitorEventSui end");
}

async function getWithdrawSui(client, lastCursor) {
  logger.info("getWithdrawSui start", {
    lastCursor: lastCursor,
  });

  let currentCursor = lastCursor;

  try {
    const eventQuery = {
      MoveEventType: `${config.sui.eventPackageId}::bridge::TokenWithdrawEvent`, // 假设的事件类型
    };

    const eventResult = await client.queryEvents({
      query: eventQuery,
      cursor: lastCursor,
      limit: 100,
      order: 'ascending'
    });

    if (eventResult.data?.length > 0) {
      currentCursor = eventResult.nextCursor;
      for (const event of eventResult.data) {
        const item = event.parsedJson;
        const hash = event.id.txDigest;
        const senderAddress = '0x' + Buffer.from(item.sender_address).toString('hex');
        const targetAddress = '0x' + Buffer.from(item.target_address).toString('hex');
        let ret = await db.insertBTCWithdrawEvent(
          hash,
          item.seq_num,
          item.source_chain,
          senderAddress,
          targetAddress,
          "",
          item.target_chain,
          (await getHash(hash) + item.seq_num),
          item.token_type,
          item.original_amount,
          item.amount,
          item.original_amount - item.amount,
          item.version,
          (+event.timestampMs) * 1000,
          0,
          0
        );
        if (ret != true) {
          logger.error("sui getWithdrawEvent save event failed", {
            event: event,
          });
        }
      }
      setSuiCursor(JSON.stringify(currentCursor));
    }
  } catch (e) {
    logger.error("Sui getWithdrawEventError", { exception: e?.stack || e?.message });
  }

  logger.info("getWithdrawSui end");
  return currentCursor;
}

function messageSign(serializeMessage, pk) {
  const signingKey = new ethers.SigningKey(pk);
  const signature = signingKey.sign(ethers.keccak256(serializeMessage)).serialized;
  return signature;
}

function generateMessage(ev) {
  const fromAddress = fromHex(ev.from_address);
  const targetAddress = fromHex(ev.target_address);
  const message_type_bytes = bcs
    .u8()
    .serialize(SIGN_MESSAGE_TYPE)
    .toBytes();
  const message_version_bytes = bcs.u8().serialize(SIGN_VERSION).toBytes();
  const seq_num_bytes = bcs.u64().serialize(Number(ev.sequence_number)).toBytes();
  const source_chain_bytes = bcs.u8().serialize(ev.source_chain).toBytes();
  const sender_address_bytes = bcs
    .vector(bcs.u8())
    .serialize(fromAddress)
    .toBytes();
  const target_chain_bytes = bcs.u8().serialize(ev.target_chain).toBytes();
  const target_address_bytes = bcs
    .vector(bcs.u8())
    .serialize(targetAddress)
    .toBytes();
  const token_type_bytes = bcs.u8().serialize(ev.token_type).toBytes();
  const amount_bytes = bcs.u64().serialize(ev.amount).toBytes();

  const payload = concatUint8Arrays([
    sender_address_bytes,
    target_chain_bytes,
    target_address_bytes,
    token_type_bytes,
    amount_bytes.reverse(),
  ]);

  const serializeMessage = concatUint8Arrays([
    message_type_bytes,
    message_version_bytes,
    seq_num_bytes.reverse(),
    source_chain_bytes,
    payload,
  ]);
  return serializeMessage;
}

// 验证签名
function verifySignature(
  serializeMessage,
  signatureHex
) {
  const messageHash = ethers.keccak256(serializeMessage);
  const recoveredAddress = ethers.recoverAddress(ethers.getBytes(messageHash), signatureHex);
  return recoveredAddress;
}

export async function isCommitteeSui(sigs, ev) {
  let threshold = 0;

  let committee = config.sui.committees;
  const serializeMessage = generateMessage(ev);
  const committeeMap = {};
  for (const member of committee) {
    committeeMap[member.toLowerCase()] = true;
  }

  for (let i = 0; i < sigs.length; i++) {
    const address = verifySignature(serializeMessage, sigs[i]);
    if (committeeMap[address.toLowerCase()]) {
      threshold = threshold + 1;
    }
  }
  return threshold > config.threshold;
}

export async function isCommitteeSuiSingle(sig, ev) {

  let committee = config.sui.committees;
  const serializeMessage = generateMessage(ev);
  const committeeMap = {};
  for (const member of committee) {
    committeeMap[member.toLowerCase()] = true;
  }

  const address = verifySignature(serializeMessage, sig);
  if (committeeMap[address.toLowerCase()]) {
    return true;
  }
  return false;
}


function concatUint8Arrays(arrays) {
  let totalLength = arrays.reduce((sum, arr) => sum + arr.length, 0); // 计算总长度
  let result = new Uint8Array(totalLength); // 创建目标数组
  let offset = 0;

  for (let arr of arrays) {
    result.set(arr, offset); // 复制数据
    offset += arr.length;
  }
  return result;
}