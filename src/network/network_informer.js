import TelegramBot from "node-telegram-bot-api";
import logger from "../component/logger.js";
import db from "../component/db.js";

setTimeout(async () => {
    await db.initDatabase();
}, 2000);

const token = process.env.TG_TOKEN;
const chatId = process.env.TG_CHAT_ID;

console.log(token);
console.log(chatId);

const bot = new TelegramBot(token, { polling: false });


const throttleObject = {};

export async function sendNotificationThrottle(message, json, tag) {
    try {
        if (!throttleObject[tag]) {
            throttleObject[tag] = 0;
        }
        throttleObject[tag]++;
        if (throttleObject[tag] % 60 !== 1) {
            return;
        }
        throttleObject[tag] = 0;
        if (json) {
            message = message + '\n<pre>' + JSON.stringify(json, null, 2) + '</pre>'
        }
        await bot.sendMessage(chatId, message, {
            parse_mode: 'html'
        });
        await db.insertSendMsg(message, 1, 3, 1);
    } catch (err) {
        logger.error("sendNotification exeception:", { msg: message, exception: err });
    }
}


export async function sendNotification(message, json) {
    try {
        if (json) {
            message = message + '\n<pre>' + JSON.stringify(json, null, 2) + '</pre>'
        }
        await bot.sendMessage(chatId, message, {
            parse_mode: 'html'
        });
        await db.insertSendMsg(message, 1, 3, 1)
    } catch (err) {
        logger.error("sendNotification exeception:", { msg: message, exception: err });
    }
}

export async function sendEventMsg(message, json, level) {
    try {
        if (json) {
            message = message + '\n<pre>' + JSON.stringify(json, null, 2) + '</pre>'
        } // 1=桥事件  3=异常 1=日志告警  2=默认 事件
        await bot.sendMessage(chatId, message, {
            parse_mode: 'html'
        });
        await db.insertSendMsg(message, 1, level, 2)
    } catch (err) {
        logger.error("sendEventMsg exeception:", { msg: message, exception: err });
    }
}

export async function sendHtml(hash, nonce, link, message, json) {
    try {

        let href = link + hash
        let content = "<a href='" + href + "'>" + message + " " + JSON.stringify(json, null, 2) + "</a>";
        await bot.sendMessage(chatId, content, {
            parse_mode: 'html'
        });
        // 1=桥事件 3=异常 1=日志告警
        await db.insertSendMsg(message, 1, 3, 1)

    } catch (err) {
        logger.error("sendHtml exeception:", { msg: message, exception: err });
    }
}
