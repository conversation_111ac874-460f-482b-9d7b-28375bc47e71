/* eslint-disable no-undef */
import dotenv from "dotenv";
import * as fs from 'fs';

const envConfig = dotenv.config({
    path: '.env-local',
    encoding: 'utf8',
    debug: false,
}).parsed;

// config.b2aptos = 1. 全都执行 2.aptos -> b2 3. movement -> b2

const config = {
    nodetype: process.env.NODE_TYPE,
    threshold: process.env.THRESHOLD,
    interval: process.env.SLEEP_INTERVAL ? parseInt(process.env.SLEEP_INTERVAL) : 2000,
    port: process.env.PORT,
    singlelimit: process.env.SINGLE_LIMIT ? parseInt(process.env.SINGLE_LIMIT) : 6000000000,
    singlecountlimit: process.env.SINGLE_COUNT_LIMIT ? parseInt(process.env.SINGLE_COUNT_LIMIT) : 100,
    singlecountamtlimit: process.env.SINGLE_COUNT_AMT_LIMIT ? parseInt(process.env.SINGLE_COUNT_AMT_LIMIT) : 6000000000,
    risksec: process.env.RISK_SEC ? parseInt(process.env.RISK_SEC) : 200,
    risksecbtc: process.env.RISK_SEC_BTC ? parseInt(process.env.RISK_SEC_BTC) : 200,
    // urltoken: "8h18@#vhi12jSG!9PPk",
    network: process.env.NETWORK,
    db: {
        host: process.env.MYSQL_HOST,
        port: process.env.MYSQL_PORT,
        user: process.env.MYSQL_USER,
        password: process.env.MYSQL_PASS,
        database: process.env.MYSQL_DBNAME
    },
    ethereum: {
        network: process.env.ETH_NETWORK,
        vaultAddress: process.env.ETH_VAULT_ADDRESS,
        bridgeAddress: process.env.ETH_BRIDGE_ADDRESS,
        tokenAddress: process.env.ETH_TOKEN_ADDRESS,
        limitAddress: process.env.ETH_LIMIT_ADDRESS,
        committerAddress: process.env.ETH_COMMITTER_ADDRESS,
        recoveryPhrase: process.env.ETH_PK,
        rpcUrl: process.env.ETH_RPC_URL,
        chainId: process.env.ETH_CHAIN_ID ? parseInt(process.env.ETH_CHAIN_ID) : undefined,
    },
    sui: {
        network: process.env.NETWORK,
        packageId: process.env.SUI_PACKAGE_ID,
        eventPackageId: process.env.SUI_EVENT_PACKAGE_ID,
        bridgeObjectId: process.env.SUI_BRIDGE_OBJECT_ID,
        rpcurl: process.env.SUI_RPC,
        recoveryPhrase: process.env.SUI_PK,
        committees: process.env.COMMITTEE_SUI ? JSON.parse(process.env.COMMITTEE_SUI) : [],
        chainId: process.env.TARGET_SUI_CHAIN_ID ? parseInt(process.env.TARGET_SUI_CHAIN_ID) : undefined,
        sbtcCoinType: process.env.SBTC_COIN_TYPE || "",
    },
    committee_b2: process.env.COMMITTEE_B2,
    B2BridgeAbi: JSON.parse(fs.readFileSync("./src/abi/abi.json", "utf8")).abi,
    b2aptos: process.env.B2APT,
    risk: true,
    passaddr: process.env.PASS_ADDRESS || "******************************************",
    checkLimitChainTokens: ['0', '10', '6', '1'],
    manualReviewChainTokens: [4], // wbtc
    superWhiteList: [],
    signatureUpdateUrl: process.env.SIGNATURE_UPDATE_URL,
    scanEvent: process.env.SCAN_EVENT, // true or false, default true
    aptos_b2: {
        singleLimit: process.env.APTOS_B2_SINGLE_LIMIT ? parseInt(process.env.APTOS_B2_SINGLE_LIMIT) : 10000000000,
        dayLimit: process.env.APTOS_B2_DAY_LIMIT ? parseInt(process.env.APTOS_B2_DAY_LIMIT) : 20000000000,
    }
};

export default config;
