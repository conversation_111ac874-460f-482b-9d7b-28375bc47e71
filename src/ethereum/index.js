import config from "../config.js";
import logger from "../component/logger.js";
import db from "../component/db.js";
import util from "../component/util.js";
import axios from "axios";
import { 
  is_committee_b2, 
  getHashPrefix, 
  checkIfSignAll, 
  message_payload_encode_sol, 
  message_encode_sol, 
  message_sign_sol,
} from "../component/util.js";
import { ethers } from "ethers";
import { prerisk, afterrisk } from "../risk.js";
import { sendNotification, sendHtml } from "../network/network_informer.js";
import { 
  CHAINID_BTC, 
  MESSAGE_TYPE, 
  VERSION,
  OVERWIRTE_ON, STATE_WHITE, STATE_PENDING, SourceChainSupport, 
  LIMIT_LOOP_MAX, 
  PATH_SIGNER,
  STATE_TOKEN_LIMIT, PATH_SENDER,
  STATE_DONE,
  AXIOS_DEFAULT_TIMEOUT,
} from "../const.js";

export async function signEthereum(overwirte, pk, transferSignature) {
  logger.info("signEthereum signer start");
 
  const currentChainId = config.ethereum.chainId;
  if (!currentChainId) {
    logger.error("signEthereum signer error: currentChainId is null");
    throw new Error("currentChainId is null");
  }
  try {
    let loopAmount = 0;
    const targetSuiChainId = config.sui.chainId;
    while (true) {
      try {
        let events = [];

        loopAmount++;
        if (config.b2aptos == SourceChainSupport.ONLY_TARGET_SUI && targetSuiChainId) {
          events = await db.selectBTCWithdrawEvent(STATE_PENDING, 5, currentChainId, targetSuiChainId);
          events = events.concat(await db.selectBTCWithdrawEvent(STATE_WHITE, 5, currentChainId, targetSuiChainId));
          if (loopAmount % LIMIT_LOOP_MAX === 0) {
            events = events.concat(await db.selectBTCWithdrawEvent(STATE_TOKEN_LIMIT, 5, currentChainId, targetSuiChainId));
          }
        }

        if (loopAmount % LIMIT_LOOP_MAX === 0) {
          loopAmount = 0;
        }

        for (let index = 0; index < events.length; index++) {
          const ev = events[index];

          // const chainTokenKey = getChainAndTokenKey(ev.target_chain, ev.token_type);
          // if (config.checkLimitChainTokens.includes(chainTokenKey)) {
          // }
          if (!transferSignature) {
            const limitAmount = await db.getWithdrawLimitAmount(ev.target_address, ev.target_chain, ev.token_type, ev.source_chain);
            if (limitAmount < 0) {
              await db.updateBTCWithdrawEvent(STATE_TOKEN_LIMIT, ev.hash);
              if (ev.state != STATE_TOKEN_LIMIT) {
                await sendNotification("Warning: Bridge Limit!", ev);
              }
              break;
            } else if (ev.state == STATE_TOKEN_LIMIT) {
              await db.updateBTCWithdrawEvent(STATE_PENDING, ev.hash);
            }
          }
          
          let seq = ev.sequence_number.toString();
          if (!checkIfSignAll(ev)) {
            continue;
          }

          const amount = BigInt(ev.amount.toString());
          const payload = message_payload_encode_sol(
            ev.from_address,
            ev.target_chain,
            ev.target_address,
            ev.token_type,
            amount);

          const Message = {
            messageType: MESSAGE_TYPE,
            version: VERSION,
            nonce: BigInt(seq),
            chainID: ev.source_chain,
            payload: payload
          };

          const encodedMessage = message_encode_sol(Message, payload);

          console.log(encodedMessage);

          let sig = [];
          let mysig = "";
          if (overwirte == OVERWIRTE_ON) {
            mysig = await message_sign_sol(encodedMessage, pk);
          } else {
            mysig = await message_sign_sol(encodedMessage, config.ethereum.recoveryPhrase);
          }
          
          if (ev.sign != "0") { 
            sig = JSON.parse(ev.sign);
          }
          try {
            if (await prerisk(ev, ev.target_chain, PATH_SIGNER)) { continue; }
            if (!sig.includes(mysig)) {
              sig.push(mysig);
              await db.updateBTCWithdrawEventSig(JSON.stringify(sig), ev.hash);
            }
            if (transferSignature) {
              const body = {
                hash: ev.hash,
                signature: mysig,
                signChainName: 'evm',
              };
              logger.info("submit eth to sender", body);
              await axios.post(config.signatureUpdateUrl, body, {timeout: AXIOS_DEFAULT_TIMEOUT}).then(async response => {
                  console.log('submit evm Response:', response.data);
                  await db.updateBTCWithdrawEvent(1, ev.hash);
              }).catch(async error => {
                  console.error('submit evm Error:', error?.message, {
                    hash: ev.hash,
                    signature: mysig,
                    signChainName: 'evm',
                  });
                  await db.updateBTCWithdrawEvent(0, ev.hash);
                });
            }
          } catch (e) {
            // sig error 
            console.log(e);
          }
        }
      } catch (e) {
        console.log(e);
      }
      await util.delay(config.interval);
    }
  } catch (e) {
    logger.error("Ethereum submitEvent", { exception: e?.stack || e?.message });
    await sendNotification("Warning: Ethereum Submitter is Shutdown!", e);
  }
  logger.info("Ethereum submitEvent end");
}

export async function submitEthereum(overwirte, pk) {
  logger.info("Ethereum sender start");

  const currentChainId = config.ethereum.chainId;
  if (!currentChainId) {
    logger.error("signEthereum signer error: currentChainId is null");
    return;
  }
  try {
    var customWsProvider = new ethers.JsonRpcProvider(config.ethereum.rpcUrl);

    let walletWithProvider;
    if (overwirte == OVERWIRTE_ON) {
      walletWithProvider = new ethers.Wallet(pk, customWsProvider);;
    } else {
      walletWithProvider = new ethers.Wallet(config.ethereum.recoveryPhrase, customWsProvider);;
    }
    const bridgeContract = new ethers.Contract(config.ethereum.bridgeAddress, config.B2BridgeAbi, walletWithProvider);

    const targetSuiChainId = config.sui.chainId;
    while (true) {
      try {
        let events = [];
        if (config.b2aptos == SourceChainSupport.ONLY_TARGET_SUI && targetSuiChainId) {
          events = await db.selectBTCWithdrawEvent(STATE_PENDING, 5, currentChainId, targetSuiChainId);
          events = events.concat(await db.selectBTCWithdrawEvent(STATE_WHITE, 5, currentChainId, targetSuiChainId));
        }

        for (let index = 0; index < events.length; index++) {
          const ev = events[index];
          let seq = ev.sequence_number.toString();
          if (ev.sign != "0") {
            let sig = JSON.parse(ev.sign);
            if (sig.length > config.threshold) {
              let res = await is_committee_b2(sig, ev);
              if (!res) { continue; }

              //风控
              if (await prerisk(ev, ev.target_chain, PATH_SENDER)) { continue; }

              try {
                const amount = BigInt(ev.amount.toString());
                const payload = message_payload_encode_sol(ev.from_address, ev.target_chain, ev.target_address, ev.token_type, amount);

                const Message = {
                  messageType: MESSAGE_TYPE,
                  version: VERSION,
                  nonce: BigInt(seq),
                  chainID: ev.source_chain,
                  payload: payload
                };

                let gasOptions;
                if (config.network === 'testnet' && ev.target_chain === 4) {
                  const feeData = await customWsProvider.getFeeData();
                  const estimatedGas = await bridgeContract.transferBridgedTokensWithSignatures.estimateGas(sig, Message);

                  gasOptions = {
                    gasLimit: estimatedGas * 120n / 100n, // 增加 20% 缓冲
                    maxPriorityFeePerGas: feeData.maxPriorityFeePerGas * 100n,
                    maxFeePerGas: feeData.maxFeePerGas * 100n
                  };
                }
                
                let tx = await bridgeContract.transferBridgedTokensWithSignatures(sig, Message, gasOptions);
                console.log("The tx of ethereum hash is: " + tx.hash);
                await tx.wait();
                await db.updateBTCWithdrawEventTargetHashAndState(ev.hash, STATE_DONE, tx.hash);
                await afterrisk(ev);
              }
              catch (e) {
                let error = "Warning: Movement Signer Failed to sign transaction at" + seq + " ";
                logger.error("source eth withdraw failed!", { event: ev, exception: e?.stack || e?.message });
                if (e && e.message) {
                  error = e.message;
                }
                await db.updateBTCWithdrawEventWithFail(2, ev.hash, error);
                await sendHtml(
                  ev.hash, seq,
                  "https://explorer.bsquared.network/tx/",
                  "Warning: Signer Failed to sign transaction at" + seq + " ",
                  e
                );
              }
            }
          }
        }
      } catch (e) {
        await sendNotification("Warning: Ethereum Submitter Meet Problem!", e);
        logger.error("submitEvent", { exception: e?.stack || e?.message });
      }
      await util.delay(config.interval);
    }
  } catch (e) {
    logger.error("Ethereum submitEvent", { exception: e?.stack || e?.message });
    await sendNotification("Warning: Ethereum Submitter is Shutdown!", e);
  }

  logger.info("Ethereum submitEvent end");
}

export async function monitorEventEthereum() {
  logger.info("ethereum monitorEvent start");

  try {
    var customWsProvider = new ethers.JsonRpcProvider(config.ethereum.rpcUrl);
    const bridgeContract = new ethers.Contract(
      config.ethereum.bridgeAddress,
      config.B2BridgeAbi,
      customWsProvider
    );

    let lastDepositBlockNumber = await db.loadDepositLastBlockNumber(config.ethereum.chainId);
    lastDepositBlockNumber = parseInt(lastDepositBlockNumber || 0);
    if (lastDepositBlockNumber === 0) {
      lastDepositBlockNumber = await customWsProvider.getBlockNumber() - 10000;
    }
    while (true) {
      try {
        let currentBlockNumber = await customWsProvider.getBlockNumber();
        if (currentBlockNumber - lastDepositBlockNumber > 2000) {
          currentBlockNumber = lastDepositBlockNumber + 2000;
        }
        await getDepositEthereum(bridgeContract, lastDepositBlockNumber, currentBlockNumber);
        lastDepositBlockNumber = currentBlockNumber;
      } catch (e) {
        logger.error("ethereum monitorEvent", { exception: e?.message });
      }
      await util.delay(config.interval); // 1 second
    }
  } catch (e) {
    logger.error("ethereum Monitor Shutdown ", { exception: e?.stack || e?.message });
    await sendNotification("Warning: ethereum Monitor is Shutdown!", e);
  }

  logger.info("monitorEvent end");
}

export async function getDepositEthereum(
  bridgeContract,
  lastBlockNumber,
  currentBlockNumber
) {
  logger.info("ethereum getDepositEvent start", {
    lastBlockNumber: lastBlockNumber,
    currentBlockNumber: currentBlockNumber,
  });

  try {
    let filter = bridgeContract.filters.TokensDeposited;
    let events = await bridgeContract.queryFilter(
      filter,
      lastBlockNumber,
      currentBlockNumber
    );

    for (let index = 0; index < events.length; index++) {
      const ev = events[index];

      const timestamp = Date.now() * 1000;
      let ret = await db.insertBTCDepositEvent(
        ev.transactionHash,
        ev.blockNumber,
        ev.args[0], // config.chainID()
        ev.args[6], // msg.sender
        ev.args[7], // recipientAddress
        "",
        ev.args[2], // destinationChainID
        (await getHashPrefix(ev.transactionHash) + ev.args[1]),
        ev.args[3], // tokenID
        ev.args[5], // originalAmount
        ev.args[4], // aptosAdjustedAmount
        (ev.args[5] - ev.args[4]),
        ev.args[1], // nonces[BridgeUtils.TOKEN_TRANSFER]
        timestamp,
        0,
        0
      );
      if (ret != true) {
        logger.error("getDepositEvent save event failed", { event: ev });
      } else if (ev.args[2] === CHAINID_BTC) {
        await set_btc_proof(ev.transactionHash, ev.args[2]);
      }
    }
  } catch (e) {
    logger.error("getDepositEvent From eth", { exception: e?.message });
  }

  logger.info("getDepositEvent end");
}

async function set_btc_proof(hash, target_chain) {
  if (target_chain !== 2) {
    return;
  }
  let prof = null;
  const ev = await db.selectBTCWithdrawEventByhash(hash, target_chain);
  if (!ev) {
    logger.error("ev not find ", { hash: hash, target_chain: target_chain });
    return;
  }
  // if target_chain 2 set prof
  prof = await util.sha3_256_btc_data(ev);
  await db.updateBTCWithdrawEventProof(hash, target_chain, prof);

}