import config from "./config.js";
import logger from "./component/logger.js";
import db from "./component/db.js";
import axios from "axios";
import { message_verify_apt } from "./component/util.js";
import { sendEventMsg, sendNotification } from "./network/network_informer.js"
import { STATE_BLACK, ERRORTYPE_RISK, CHAINID_B2, CHAINID_BTC, CHAINID_APT, CHAINID_MOVE, PATH_SIGNER, PATH_SENDER, EVENTTYPE_BRIDGE, ERROR_LEVEL_INFO, ERROR_LEVEL_WARNING, STATE_REORG, STATE_RISK, STATE_BTCNOPROOF, CHAINID_MORPHL, AXIOS_DEFAULT_TIMEOUT, STATE_WHITE, STATE_MANUAL_REVIEW } from "./const.js";
import { sha3_256_btc_data } from "./component/util.js"
import { gethash } from "./component/util.js"

let chainlist = {
    1: "B2",
    2: "BTC",
    11: "APTOS",
    12: "MOVEMENT"
}

// 风控规则
// Pre
// 1 - 时间, 2 - 限额（不写), 3 - 出入境记录, 4 - 黑名单, 5 - 重放
// After
// 1 - 限额（填写） 2 - 白名单（发送）
// state = 100 特许放行
// state 4 = 黑名单冻结 5 = 白名单处理 6 = 外部API异常或者程序错误 7 = BTCPROOF验证错误 8 = 限额 9 = 重放

export async function afterrisk(ev) {
    if (ev.state == 100 || config.risk == false) { return false }

    let [white, msg_json] = await check_white_list(ev)
    if (white) {
        logger.warn("find white user ", {
            from_address: ev.from_address, target_address: ev.target_address,
            source_chain: ev.source_chain, target_chain: ev.target_chain
        })

        await db.insertSendMsg(`User is in the white list ${ev.hash}`, EVENTTYPE_BRIDGE, ERROR_LEVEL_INFO, ERRORTYPE_RISK)
    }
}

export async function generatePreMessage(ev) {
    const preMessage = await gethash(ev.hash + ev.sequence_number + ev.amount + ev.from_address + ev.target_address);
    return preMessage;
}

export async function prerisk(ev, flag, path) {

    if (ev.state != STATE_WHITE && config.manualReviewChainTokens.includes(Number(ev.token_type)) && ev.target_chain === config.ethereum.chainId) { // 人工审核
        await db.updateBTCWithdrawEventWithFail(STATE_MANUAL_REVIEW, ev.hash, 'need manual review');
        return true;
    }

    // TODO: Detailed Info
    if (config.risk == false) { return false }

    try {
        const preMessage = await generatePreMessage(ev);
        switch (path) {
            case PATH_SENDER:
                if (ev.state == 100) {
                    if (await message_verify_apt(ev.proof, preMessage) != config.passaddr) { return true }
                    return false
                }
                break;
            case PATH_SIGNER:
                if (ev.state == 100) {
                    if (await message_verify_apt(ev.proof, preMessage) != config.passaddr) { return true }
                    return false
                }
                else {
                    if (ev.source_chain === 2 && !await check_btc_hash(ev)) {
                        return true
                    }
                }
                break;
        }

        // TIME
        // ADD SPECIAL BITCOIN TIME
        if (checkTime(ev)) { console.log("RISK: TIME"); return true }

        // Blacklist
        let black = await check_black_list(ev)
        if (black) {
            logger.warn("find black user ", {
                from_address: ev.from_address, target_address: ev.target_address,
                source_chain: ev.source_chain, target_chain: ev.target_chain
            })
            console.log("RISK: BLACK");
            await wirteErrorMsg(ev.target_chain, STATE_BLACK, ev.hash, "User in blacklist", path);
            return true
        }

        if (ev.amount > config.singlelimit) { // Single Limit  JUST WARNING, NOT BLOCK
            logger.warn("User reached single limit", {
                from_address: ev.from_address, target_address: ev.target_address,
                source_chain: ev.source_chain, target_chain: ev.target_chain
            });
            console.log("RISK: SINGLELIMITAMOUNT");
            // await wirteErrorMsg(flag, STATE_RISK, ev.hash, "User reached single limit", path);
            // await db.insertSendMsg(`User reached single limit ${ev.hash}`, EVENTTYPE_BRIDGE, ERROR_LEVEL_WARNING, ERRORTYPE_RISK);
            // return true
        }

        // NO TOTAL LIMIT, CAUSE THE CONTRACT DOES IT
        // In Out
        // For a B2 withdraw event, check In/out, from b2 -> x, skip check
        // JUST WARNING, NOT BLOCK
        const currentDate = new Date();
        const lastdayDate = currentDate - 86400000;

        // Count Limit
        // JUST WARNING, NOT BLOCK
        // TODO
        // ONLY CHECK WITHDRAW?
        // let t_c = await db.selectBTCDepositEventByAddressStateTime(ev.target_address, 1, ev.source_chain, ev.target_chain, currentDate - 86400000)
        // t_c = t_c.concat(await db.selectBTCWithdrawEventByAddressStateTime(ev.from_address, 1, ev.source_chain, ev.target_chain, currentDate - 86400000))
        let t_c
        let t_a
        try {
            t_c = await db.selectBTCWithdrawEventByAddressStateTime(ev.target_address, 1, ev.source_chain, ev.target_chain, lastdayDate)
            if (t_c.length > config.singlecountlimit) {
                console.log("RISK: CONUTLIMIT");
                if (path == PATH_SENDER) { await db.insertSendMsg(`User has too many transaction ${ev.hash}`, EVENTTYPE_BRIDGE, ERROR_LEVEL_WARNING, ERRORTYPE_RISK) }
            }
            t_a = t_c.reduce((sum, item) => { return sum + Number(item.amount); }, 0);
            if (t_a > config.singlecountamtlimit) {
                console.log("RISK: CONUTAMTLIMIT");
                if (path == PATH_SENDER) { await db.insertSendMsg(`User transaction reached amt limit ${ev.hash}`, EVENTTYPE_BRIDGE, ERROR_LEVEL_WARNING, ERRORTYPE_RISK) }
            }
        } catch (e) {
            if (!t_c.length) { console.log('RISK: No withdraw, good') } else { console.log(e) }
        }

        // REORG
        if (path == PATH_SENDER) {
            const reorg = await checkReorg(ev).catch(e => {
                logger.error("Risk Check Meet Problem!", e);
                return false;
            });
            if (reorg) {
                console.log("RISK: REORG");
                await wirteErrorMsg(ev.target_chain, STATE_REORG, ev.hash, `User tx ${chainlist[ev.source_chain]} to b2 Failed with transaction not find`, path);
                return true
            }
        }

        return false;
    } catch(e) {
        logger.error("Risk Check Meet Problem!", e);
        return true;
    }
}

async function checkReorg(ev) {
    return false;
}

function checkTime(ev) {
    let timestampDate = new Date(ev.created)
    // switch (ev.source_chain) {
    //     case CHAINID_BTC:
    //         timestampDate = new Date(ev.timestamp * 1000)
    //         break;
    //     case CHAINID_APT:
    //         timestampDate = new Date(ev.timestamp / 1000)
    //         break;
    //     case CHAINID_MOVE:
    //         timestampDate = new Date(ev.timestamp / 1000)
    //         break;
    //     default:
    //         timestampDate = new Date(ev.timestamp)
    //         break;
    // }
    const currentDate = new Date();
    const timeDifference = currentDate - timestampDate;

    let RiskMinutesInMilliseconds = config.risksec * 1000;
    if (ev.source_chain == CHAINID_BTC) { RiskMinutesInMilliseconds = config.risksecbtc * 1000 }

    if (timeDifference < RiskMinutesInMilliseconds) {
        return true
    }

    return false
}

async function check_white_list(ev) {

    const msg_json = {
        hash: ev.hash, address: ev.from_address,
        chain: ev.source_chain, content: `from ${ev.source_chain} to ${ev.target_chain}`
    }

    let source_white = await db.checkInBridgeList(ev.from_address, ev.source_chain, 1)
    if (!source_white) {
        source_white = await db.checkInBridgeList(ev.target_address, ev.target_chain, 1)
        if (source_white) {
            msg_json.address = ev.target_address
            msg_json.chain = ev.target_chain
        }
    }
    // send white name msg
    if (source_white) {
        // send white
        await sendEventMsg("A whitelist has been added ", msg_json, 2)

        return [true, msg_json]
    }

    return [false, null]
}

async function check_black_list(ev) {

    const msg_json = {
        hash: ev.hash, address: ev.from_address,
        chain: ev.source_chain, content: `from ${ev.source_chain} to ${ev.target_chain}`
    }

    let black_address = await db.checkInBridgeList(ev.from_address, ev.source_chain, 2)
    if (!black_address) {
        black_address = await db.checkInBridgeList(ev.target_address, ev.target_chain, 2)
        if (black_address) {
            msg_json.address = ev.target_address
            msg_json.chain = ev.target_chain
        }
    }

    // send black name msg
    if (black_address) {
        await sendEventMsg("A blacklist has been added ", msg_json, 2)
        return true
    }

    return false
}

async function wirteErrorMsg(flag, index, hash, msg, path) {
    try {
        const targetEthChainId = config.target_ethereum.chainId;
        if (flag == CHAINID_APT || flag == CHAINID_MOVE || flag == CHAINID_MORPHL || (targetEthChainId && flag == targetEthChainId)) {
            await db.insertSendMsg(msg + ` ${hash}`, EVENTTYPE_BRIDGE, ERROR_LEVEL_WARNING, ERRORTYPE_RISK)
            await db.updateBTCDepositEventWithFail(index, hash, msg);
        }
        else {
            await db.insertSendMsg(msg + ` ${hash}`, EVENTTYPE_BRIDGE, ERROR_LEVEL_WARNING, ERRORTYPE_RISK)
            await db.updateBTCWithdrawEventWithFail(index, hash, msg);
        }
    }
    catch (e) { console.log(e) }
}

async function check_btc_hash(ev) {

    const hash = await sha3_256_btc_data(ev)
    if (ev.proof && ev.proof === hash) {
        return true;
    }
    // hash invalid  send msg
    await sendNotification("BTC verification hash failed hash=" + hash)

    await db.updateBTCDepositEvent(STATE_BTCNOPROOF, ev.hash)
    return false;
}
